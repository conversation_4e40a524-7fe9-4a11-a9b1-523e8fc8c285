{"type":"summary","summary":"Local Command Execution: MCP Command Attempted","leafUuid":"53db6a66-f63c-4b9b-a1fd-84308ffdaf15"}
{"type":"summary","summary":"Local Command Exit and Empty Output","leafUuid":"dfabc534-aa5a-43d7-8858-1c3c952c5ee6"}
{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":"@firecrawl-mcp-server  vs @mcp-crawl4ai-rag which mcp server is better for document crawler for rag purpose? Give me your reason. "},"uuid":"b5327ea9-86a2-4ac3-801e-146239588a7e","timestamp":"2025-07-11T01:36:04.304Z"}
{"parentUuid":"b5327ea9-86a2-4ac3-801e-146239588a7e","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_017aMQnCDTDfgfmcvzxcZEt6","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_017U5XAhXZjic12Z3qQaTPFS","name":"Read","input":{"file_path":"/home/<USER>/development/tools/mcp_servers/firecrawl-mcp-server/README.md"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":5208,"cache_read_input_tokens":10491,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNdk8K1Vp8AvzoHXCir","type":"assistant","uuid":"cf0f1656-4e27-451a-aac6-2e400a858fdf","timestamp":"2025-07-11T01:36:07.345Z"}
{"parentUuid":"cf0f1656-4e27-451a-aac6-2e400a858fdf","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_017aMQnCDTDfgfmcvzxcZEt6","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01Q1nKjViuqTmNQnWv2FCTBn","name":"Read","input":{"file_path":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":5208,"cache_read_input_tokens":10491,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNdk8K1Vp8AvzoHXCir","type":"assistant","uuid":"0bd8618b-0be6-4264-9a17-b89b5790ba27","timestamp":"2025-07-11T01:36:08.013Z"}
{"parentUuid":"0bd8618b-0be6-4264-9a17-b89b5790ba27","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_017aMQnCDTDfgfmcvzxcZEt6","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01LWZV5Nqso73zNGour4NAE7","name":"Read","input":{"file_path":"/home/<USER>/development/tools/mcp_servers/firecrawl-mcp-server/package.json"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":5208,"cache_read_input_tokens":10491,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNdk8K1Vp8AvzoHXCir","type":"assistant","uuid":"3f224b46-1197-433e-aba5-f16100a43e76","timestamp":"2025-07-11T01:36:08.584Z"}
{"parentUuid":"3f224b46-1197-433e-aba5-f16100a43e76","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_017aMQnCDTDfgfmcvzxcZEt6","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01D5fvCHtVnQWh3rveMdNb2Y","name":"Read","input":{"file_path":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/pyproject.toml"}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":5208,"cache_read_input_tokens":10491,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNdk8K1Vp8AvzoHXCir","type":"assistant","uuid":"195ea596-21c2-4422-99d4-62d12d523411","timestamp":"2025-07-11T01:36:09.537Z"}
{"parentUuid":"195ea596-21c2-4422-99d4-62d12d523411","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_017U5XAhXZjic12Z3qQaTPFS","type":"tool_result","content":"     1→# Firecrawl MCP Server\n     2→\n     3→A Model Context Protocol (MCP) server implementation that integrates with [Firecrawl](https://github.com/mendableai/firecrawl) for web scraping capabilities.\n     4→\n     5→> Big thanks to [@vrknetha](https://github.com/vrknetha), [@knacklabs](https://www.knacklabs.ai) for the initial implementation!\n     6→\n     7→\n     8→## Features\n     9→\n    10→- Web scraping, crawling, and discovery\n    11→- Search and content extraction\n    12→- Deep research and batch scraping\n    13→- Automatic retries and rate limiting\n    14→- Cloud and self-hosted support\n    15→- SSE support\n    16→\n    17→> Play around with [our MCP Server on MCP.so's playground](https://mcp.so/playground?server=firecrawl-mcp-server) or on [Klavis AI](https://www.klavis.ai/mcp-servers).\n    18→\n    19→## Installation\n    20→\n    21→### Running with npx\n    22→\n    23→```bash\n    24→env FIRECRAWL_API_KEY=fc-YOUR_API_KEY npx -y firecrawl-mcp\n    25→```\n    26→\n    27→### Manual Installation\n    28→\n    29→```bash\n    30→npm install -g firecrawl-mcp\n    31→```\n    32→\n    33→### Running on Cursor\n    34→\n    35→Configuring Cursor 🖥️\n    36→Note: Requires Cursor version 0.45.6+\n    37→For the most up-to-date configuration instructions, please refer to the official Cursor documentation on configuring MCP servers:\n    38→[Cursor MCP Server Configuration Guide](https://docs.cursor.com/context/model-context-protocol#configuring-mcp-servers)\n    39→\n    40→To configure Firecrawl MCP in Cursor **v0.48.6**\n    41→\n    42→1. Open Cursor Settings\n    43→2. Go to Features > MCP Servers\n    44→3. Click \"+ Add new global MCP server\"\n    45→4. Enter the following code:\n    46→   ```json\n    47→   {\n    48→     \"mcpServers\": {\n    49→       \"firecrawl-mcp\": {\n    50→         \"command\": \"npx\",\n    51→         \"args\": [\"-y\", \"firecrawl-mcp\"],\n    52→         \"env\": {\n    53→           \"FIRECRAWL_API_KEY\": \"YOUR-API-KEY\"\n    54→         }\n    55→       }\n    56→     }\n    57→   }\n    58→   ```\n    59→   \n    60→To configure Firecrawl MCP in Cursor **v0.45.6**\n    61→\n    62→1. Open Cursor Settings\n    63→2. Go to Features > MCP Servers\n    64→3. Click \"+ Add New MCP Server\"\n    65→4. Enter the following:\n    66→   - Name: \"firecrawl-mcp\" (or your preferred name)\n    67→   - Type: \"command\"\n    68→   - Command: `env FIRECRAWL_API_KEY=your-api-key npx -y firecrawl-mcp`\n    69→\n    70→\n    71→\n    72→> If you are using Windows and are running into issues, try `cmd /c \"set FIRECRAWL_API_KEY=your-api-key && npx -y firecrawl-mcp\"`\n    73→\n    74→Replace `your-api-key` with your Firecrawl API key. If you don't have one yet, you can create an account and get it from https://www.firecrawl.dev/app/api-keys\n    75→\n    76→After adding, refresh the MCP server list to see the new tools. The Composer Agent will automatically use Firecrawl MCP when appropriate, but you can explicitly request it by describing your web scraping needs. Access the Composer via Command+L (Mac), select \"Agent\" next to the submit button, and enter your query.\n    77→\n    78→### Running on Windsurf\n    79→\n    80→Add this to your `./codeium/windsurf/model_config.json`:\n    81→\n    82→```json\n    83→{\n    84→  \"mcpServers\": {\n    85→    \"mcp-server-firecrawl\": {\n    86→      \"command\": \"npx\",\n    87→      \"args\": [\"-y\", \"firecrawl-mcp\"],\n    88→      \"env\": {\n    89→        \"FIRECRAWL_API_KEY\": \"YOUR_API_KEY\"\n    90→      }\n    91→    }\n    92→  }\n    93→}\n    94→```\n    95→\n    96→### Running with SSE Local Mode\n    97→\n    98→To run the server using Server-Sent Events (SSE) locally instead of the default stdio transport:\n    99→\n   100→```bash\n   101→env SSE_LOCAL=true FIRECRAWL_API_KEY=fc-YOUR_API_KEY npx -y firecrawl-mcp\n   102→```\n   103→\n   104→Use the url: http://localhost:3000/sse\n   105→\n   106→### Installing via Smithery (Legacy)\n   107→\n   108→To install Firecrawl for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@mendableai/mcp-server-firecrawl):\n   109→\n   110→```bash\n   111→npx -y @smithery/cli install @mendableai/mcp-server-firecrawl --client claude\n   112→```\n   113→\n   114→### Running on VS Code\n   115→\n   116→For one-click installation, click one of the install buttons below...\n   117→\n   118→[![Install with NPX in VS Code](https://img.shields.io/badge/VS_Code-NPM-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=firecrawl&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22apiKey%22%2C%22description%22%3A%22Firecrawl%20API%20Key%22%2C%22password%22%3Atrue%7D%5D&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22firecrawl-mcp%22%5D%2C%22env%22%3A%7B%22FIRECRAWL_API_KEY%22%3A%22%24%7Binput%3AapiKey%7D%22%7D%7D) [![Install with NPX in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-NPM-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=firecrawl&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22apiKey%22%2C%22description%22%3A%22Firecrawl%20API%20Key%22%2C%22password%22%3Atrue%7D%5D&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22firecrawl-mcp%22%5D%2C%22env%22%3A%7B%22FIRECRAWL_API_KEY%22%3A%22%24%7Binput%3AapiKey%7D%22%7D%7D&quality=insiders)\n   119→\n   120→For manual installation, add the following JSON block to your User Settings (JSON) file in VS Code. You can do this by pressing `Ctrl + Shift + P` and typing `Preferences: Open User Settings (JSON)`.\n   121→\n   122→```json\n   123→{\n   124→  \"mcp\": {\n   125→    \"inputs\": [\n   126→      {\n   127→        \"type\": \"promptString\",\n   128→        \"id\": \"apiKey\",\n   129→        \"description\": \"Firecrawl API Key\",\n   130→        \"password\": true\n   131→      }\n   132→    ],\n   133→    \"servers\": {\n   134→      \"firecrawl\": {\n   135→        \"command\": \"npx\",\n   136→        \"args\": [\"-y\", \"firecrawl-mcp\"],\n   137→        \"env\": {\n   138→          \"FIRECRAWL_API_KEY\": \"${input:apiKey}\"\n   139→        }\n   140→      }\n   141→    }\n   142→  }\n   143→}\n   144→```\n   145→\n   146→Optionally, you can add it to a file called `.vscode/mcp.json` in your workspace. This will allow you to share the configuration with others:\n   147→\n   148→```json\n   149→{\n   150→  \"inputs\": [\n   151→    {\n   152→      \"type\": \"promptString\",\n   153→      \"id\": \"apiKey\",\n   154→      \"description\": \"Firecrawl API Key\",\n   155→      \"password\": true\n   156→    }\n   157→  ],\n   158→  \"servers\": {\n   159→    \"firecrawl\": {\n   160→      \"command\": \"npx\",\n   161→      \"args\": [\"-y\", \"firecrawl-mcp\"],\n   162→      \"env\": {\n   163→        \"FIRECRAWL_API_KEY\": \"${input:apiKey}\"\n   164→      }\n   165→    }\n   166→  }\n   167→}\n   168→```\n   169→\n   170→## Configuration\n   171→\n   172→### Environment Variables\n   173→\n   174→#### Required for Cloud API\n   175→\n   176→- `FIRECRAWL_API_KEY`: Your Firecrawl API key\n   177→  - Required when using cloud API (default)\n   178→  - Optional when using self-hosted instance with `FIRECRAWL_API_URL`\n   179→- `FIRECRAWL_API_URL` (Optional): Custom API endpoint for self-hosted instances\n   180→  - Example: `https://firecrawl.your-domain.com`\n   181→  - If not provided, the cloud API will be used (requires API key)\n   182→\n   183→#### Optional Configuration\n   184→\n   185→##### Retry Configuration\n   186→\n   187→- `FIRECRAWL_RETRY_MAX_ATTEMPTS`: Maximum number of retry attempts (default: 3)\n   188→- `FIRECRAWL_RETRY_INITIAL_DELAY`: Initial delay in milliseconds before first retry (default: 1000)\n   189→- `FIRECRAWL_RETRY_MAX_DELAY`: Maximum delay in milliseconds between retries (default: 10000)\n   190→- `FIRECRAWL_RETRY_BACKOFF_FACTOR`: Exponential backoff multiplier (default: 2)\n   191→\n   192→##### Credit Usage Monitoring\n   193→\n   194→- `FIRECRAWL_CREDIT_WARNING_THRESHOLD`: Credit usage warning threshold (default: 1000)\n   195→- `FIRECRAWL_CREDIT_CRITICAL_THRESHOLD`: Credit usage critical threshold (default: 100)\n   196→\n   197→### Configuration Examples\n   198→\n   199→For cloud API usage with custom retry and credit monitoring:\n   200→\n   201→```bash\n   202→# Required for cloud API\n   203→export FIRECRAWL_API_KEY=your-api-key\n   204→\n   205→# Optional retry configuration\n   206→export FIRECRAWL_RETRY_MAX_ATTEMPTS=5        # Increase max retry attempts\n   207→export FIRECRAWL_RETRY_INITIAL_DELAY=2000    # Start with 2s delay\n   208→export FIRECRAWL_RETRY_MAX_DELAY=30000       # Maximum 30s delay\n   209→export FIRECRAWL_RETRY_BACKOFF_FACTOR=3      # More aggressive backoff\n   210→\n   211→# Optional credit monitoring\n   212→export FIRECRAWL_CREDIT_WARNING_THRESHOLD=2000    # Warning at 2000 credits\n   213→export FIRECRAWL_CREDIT_CRITICAL_THRESHOLD=500    # Critical at 500 credits\n   214→```\n   215→\n   216→For self-hosted instance:\n   217→\n   218→```bash\n   219→# Required for self-hosted\n   220→export FIRECRAWL_API_URL=https://firecrawl.your-domain.com\n   221→\n   222→# Optional authentication for self-hosted\n   223→export FIRECRAWL_API_KEY=your-api-key  # If your instance requires auth\n   224→\n   225→# Custom retry configuration\n   226→export FIRECRAWL_RETRY_MAX_ATTEMPTS=10\n   227→export FIRECRAWL_RETRY_INITIAL_DELAY=500     # Start with faster retries\n   228→```\n   229→\n   230→### Usage with Claude Desktop\n   231→\n   232→Add this to your `claude_desktop_config.json`:\n   233→\n   234→```json\n   235→{\n   236→  \"mcpServers\": {\n   237→    \"mcp-server-firecrawl\": {\n   238→      \"command\": \"npx\",\n   239→      \"args\": [\"-y\", \"firecrawl-mcp\"],\n   240→      \"env\": {\n   241→        \"FIRECRAWL_API_KEY\": \"YOUR_API_KEY_HERE\",\n   242→\n   243→        \"FIRECRAWL_RETRY_MAX_ATTEMPTS\": \"5\",\n   244→        \"FIRECRAWL_RETRY_INITIAL_DELAY\": \"2000\",\n   245→        \"FIRECRAWL_RETRY_MAX_DELAY\": \"30000\",\n   246→        \"FIRECRAWL_RETRY_BACKOFF_FACTOR\": \"3\",\n   247→\n   248→        \"FIRECRAWL_CREDIT_WARNING_THRESHOLD\": \"2000\",\n   249→        \"FIRECRAWL_CREDIT_CRITICAL_THRESHOLD\": \"500\"\n   250→      }\n   251→    }\n   252→  }\n   253→}\n   254→```\n   255→\n   256→### System Configuration\n   257→\n   258→The server includes several configurable parameters that can be set via environment variables. Here are the default values if not configured:\n   259→\n   260→```typescript\n   261→const CONFIG = {\n   262→  retry: {\n   263→    maxAttempts: 3, // Number of retry attempts for rate-limited requests\n   264→    initialDelay: 1000, // Initial delay before first retry (in milliseconds)\n   265→    maxDelay: 10000, // Maximum delay between retries (in milliseconds)\n   266→    backoffFactor: 2, // Multiplier for exponential backoff\n   267→  },\n   268→  credit: {\n   269→    warningThreshold: 1000, // Warn when credit usage reaches this level\n   270→    criticalThreshold: 100, // Critical alert when credit usage reaches this level\n   271→  },\n   272→};\n   273→```\n   274→\n   275→These configurations control:\n   276→\n   277→1. **Retry Behavior**\n   278→\n   279→   - Automatically retries failed requests due to rate limits\n   280→   - Uses exponential backoff to avoid overwhelming the API\n   281→   - Example: With default settings, retries will be attempted at:\n   282→     - 1st retry: 1 second delay\n   283→     - 2nd retry: 2 seconds delay\n   284→     - 3rd retry: 4 seconds delay (capped at maxDelay)\n   285→\n   286→2. **Credit Usage Monitoring**\n   287→   - Tracks API credit consumption for cloud API usage\n   288→   - Provides warnings at specified thresholds\n   289→   - Helps prevent unexpected service interruption\n   290→   - Example: With default settings:\n   291→     - Warning at 1000 credits remaining\n   292→     - Critical alert at 100 credits remaining\n   293→\n   294→### Rate Limiting and Batch Processing\n   295→\n   296→The server utilizes Firecrawl's built-in rate limiting and batch processing capabilities:\n   297→\n   298→- Automatic rate limit handling with exponential backoff\n   299→- Efficient parallel processing for batch operations\n   300→- Smart request queuing and throttling\n   301→- Automatic retries for transient errors\n   302→\n   303→## How to Choose a Tool\n   304→\n   305→Use this guide to select the right tool for your task:\n   306→\n   307→- **If you know the exact URL(s) you want:**\n   308→  - For one: use **scrape**\n   309→  - For many: use **batch_scrape**\n   310→- **If you need to discover URLs on a site:** use **map**\n   311→- **If you want to search the web for info:** use **search**\n   312→- **If you want to extract structured data:** use **extract**\n   313→- **If you want to analyze a whole site or section:** use **crawl** (with limits!)\n   314→- **If you want to do in-depth research:** use **deep_research**\n   315→- **If you want to generate LLMs.txt:** use **generate_llmstxt**\n   316→\n   317→### Quick Reference Table\n   318→\n   319→| Tool                | Best for                                 | Returns         |\n   320→|---------------------|------------------------------------------|-----------------|\n   321→| scrape              | Single page content                      | markdown/html   |\n   322→| batch_scrape        | Multiple known URLs                      | markdown/html[] |\n   323→| map                 | Discovering URLs on a site               | URL[]           |\n   324→| crawl               | Multi-page extraction (with limits)      | markdown/html[] |\n   325→| search              | Web search for info                      | results[]       |\n   326→| extract             | Structured data from pages               | JSON            |\n   327→| deep_research       | In-depth, multi-source research          | summary, sources|\n   328→| generate_llmstxt    | LLMs.txt for a domain                    | text            |\n   329→\n   330→## Available Tools\n   331→\n   332→### 1. Scrape Tool (`firecrawl_scrape`)\n   333→\n   334→Scrape content from a single URL with advanced options.\n   335→\n   336→**Best for:**\n   337→- Single page content extraction, when you know exactly which page contains the information.\n   338→\n   339→**Not recommended for:**\n   340→- Extracting content from multiple pages (use batch_scrape for known URLs, or map + batch_scrape to discover URLs first, or crawl for full page content)\n   341→- When you're unsure which page contains the information (use search)\n   342→- When you need structured data (use extract)\n   343→\n   344→**Common mistakes:**\n   345→- Using scrape for a list of URLs (use batch_scrape instead).\n   346→\n   347→**Prompt Example:**\n   348→> \"Get the content of the page at https://example.com.\"\n   349→\n   350→**Usage Example:**\n   351→```json\n   352→{\n   353→  \"name\": \"firecrawl_scrape\",\n   354→  \"arguments\": {\n   355→    \"url\": \"https://example.com\",\n   356→    \"formats\": [\"markdown\"],\n   357→    \"onlyMainContent\": true,\n   358→    \"waitFor\": 1000,\n   359→    \"timeout\": 30000,\n   360→    \"mobile\": false,\n   361→    \"includeTags\": [\"article\", \"main\"],\n   362→    \"excludeTags\": [\"nav\", \"footer\"],\n   363→    \"skipTlsVerification\": false\n   364→  }\n   365→}\n   366→```\n   367→\n   368→**Returns:**\n   369→- Markdown, HTML, or other formats as specified.\n   370→\n   371→### 2. Batch Scrape Tool (`firecrawl_batch_scrape`)\n   372→\n   373→Scrape multiple URLs efficiently with built-in rate limiting and parallel processing.\n   374→\n   375→**Best for:**\n   376→- Retrieving content from multiple pages, when you know exactly which pages to scrape.\n   377→\n   378→**Not recommended for:**\n   379→- Discovering URLs (use map first if you don't know the URLs)\n   380→- Scraping a single page (use scrape)\n   381→\n   382→**Common mistakes:**\n   383→- Using batch_scrape with too many URLs at once (may hit rate limits or token overflow)\n   384→\n   385→**Prompt Example:**\n   386→> \"Get the content of these three blog posts: [url1, url2, url3].\"\n   387→\n   388→**Usage Example:**\n   389→```json\n   390→{\n   391→  \"name\": \"firecrawl_batch_scrape\",\n   392→  \"arguments\": {\n   393→    \"urls\": [\"https://example1.com\", \"https://example2.com\"],\n   394→    \"options\": {\n   395→      \"formats\": [\"markdown\"],\n   396→      \"onlyMainContent\": true\n   397→    }\n   398→  }\n   399→}\n   400→```\n   401→\n   402→**Returns:**\n   403→- Response includes operation ID for status checking:\n   404→\n   405→```json\n   406→{\n   407→  \"content\": [\n   408→    {\n   409→      \"type\": \"text\",\n   410→      \"text\": \"Batch operation queued with ID: batch_1. Use firecrawl_check_batch_status to check progress.\"\n   411→    }\n   412→  ],\n   413→  \"isError\": false\n   414→}\n   415→```\n   416→\n   417→### 3. Check Batch Status (`firecrawl_check_batch_status`)\n   418→\n   419→Check the status of a batch operation.\n   420→\n   421→```json\n   422→{\n   423→  \"name\": \"firecrawl_check_batch_status\",\n   424→  \"arguments\": {\n   425→    \"id\": \"batch_1\"\n   426→  }\n   427→}\n   428→```\n   429→\n   430→### 4. Map Tool (`firecrawl_map`)\n   431→\n   432→Map a website to discover all indexed URLs on the site.\n   433→\n   434→**Best for:**\n   435→- Discovering URLs on a website before deciding what to scrape\n   436→- Finding specific sections of a website\n   437→\n   438→**Not recommended for:**\n   439→- When you already know which specific URL you need (use scrape or batch_scrape)\n   440→- When you need the content of the pages (use scrape after mapping)\n   441→\n   442→**Common mistakes:**\n   443→- Using crawl to discover URLs instead of map\n   444→\n   445→**Prompt Example:**\n   446→> \"List all URLs on example.com.\"\n   447→\n   448→**Usage Example:**\n   449→```json\n   450→{\n   451→  \"name\": \"firecrawl_map\",\n   452→  \"arguments\": {\n   453→    \"url\": \"https://example.com\"\n   454→  }\n   455→}\n   456→```\n   457→\n   458→**Returns:**\n   459→- Array of URLs found on the site\n   460→\n   461→### 5. Search Tool (`firecrawl_search`)\n   462→\n   463→Search the web and optionally extract content from search results.\n   464→\n   465→**Best for:**\n   466→- Finding specific information across multiple websites, when you don't know which website has the information.\n   467→- When you need the most relevant content for a query\n   468→\n   469→**Not recommended for:**\n   470→- When you already know which website to scrape (use scrape)\n   471→- When you need comprehensive coverage of a single website (use map or crawl)\n   472→\n   473→**Common mistakes:**\n   474→- Using crawl or map for open-ended questions (use search instead)\n   475→\n   476→**Usage Example:**\n   477→```json\n   478→{\n   479→  \"name\": \"firecrawl_search\",\n   480→  \"arguments\": {\n   481→    \"query\": \"latest AI research papers 2023\",\n   482→    \"limit\": 5,\n   483→    \"lang\": \"en\",\n   484→    \"country\": \"us\",\n   485→    \"scrapeOptions\": {\n   486→      \"formats\": [\"markdown\"],\n   487→      \"onlyMainContent\": true\n   488→    }\n   489→  }\n   490→}\n   491→```\n   492→\n   493→**Returns:**\n   494→- Array of search results (with optional scraped content)\n   495→\n   496→**Prompt Example:**\n   497→> \"Find the latest research papers on AI published in 2023.\"\n   498→\n   499→### 6. Crawl Tool (`firecrawl_crawl`)\n   500→\n   501→Starts an asynchronous crawl job on a website and extract content from all pages.\n   502→\n   503→**Best for:**\n   504→- Extracting content from multiple related pages, when you need comprehensive coverage.\n   505→\n   506→**Not recommended for:**\n   507→- Extracting content from a single page (use scrape)\n   508→- When token limits are a concern (use map + batch_scrape)\n   509→- When you need fast results (crawling can be slow)\n   510→\n   511→**Warning:** Crawl responses can be very large and may exceed token limits. Limit the crawl depth and number of pages, or use map + batch_scrape for better control.\n   512→\n   513→**Common mistakes:**\n   514→- Setting limit or maxDepth too high (causes token overflow)\n   515→- Using crawl for a single page (use scrape instead)\n   516→\n   517→**Prompt Example:**\n   518→> \"Get all blog posts from the first two levels of example.com/blog.\"\n   519→\n   520→**Usage Example:**\n   521→```json\n   522→{\n   523→  \"name\": \"firecrawl_crawl\",\n   524→  \"arguments\": {\n   525→    \"url\": \"https://example.com/blog/*\",\n   526→    \"maxDepth\": 2,\n   527→    \"limit\": 100,\n   528→    \"allowExternalLinks\": false,\n   529→    \"deduplicateSimilarURLs\": true\n   530→  }\n   531→}\n   532→```\n   533→\n   534→**Returns:**\n   535→- Response includes operation ID for status checking:\n   536→\n   537→```json\n   538→{\n   539→  \"content\": [\n   540→    {\n   541→      \"type\": \"text\",\n   542→      \"text\": \"Started crawl for: https://example.com/* with job ID: 550e8400-e29b-41d4-a716-446655440000. Use firecrawl_check_crawl_status to check progress.\"\n   543→    }\n   544→  ],\n   545→  \"isError\": false\n   546→}\n   547→```\n   548→\n   549→### 7. Check Crawl Status (`firecrawl_check_crawl_status`)\n   550→\n   551→Check the status of a crawl job.\n   552→\n   553→```json\n   554→{\n   555→  \"name\": \"firecrawl_check_crawl_status\",\n   556→  \"arguments\": {\n   557→    \"id\": \"550e8400-e29b-41d4-a716-446655440000\"\n   558→  }\n   559→}\n   560→```\n   561→\n   562→**Returns:**\n   563→- Response includes the status of the crawl job:\n   564→  \n   565→### 8. Extract Tool (`firecrawl_extract`)\n   566→\n   567→Extract structured information from web pages using LLM capabilities. Supports both cloud AI and self-hosted LLM extraction.\n   568→\n   569→**Best for:**\n   570→- Extracting specific structured data like prices, names, details.\n   571→\n   572→**Not recommended for:**\n   573→- When you need the full content of a page (use scrape)\n   574→- When you're not looking for specific structured data\n   575→\n   576→**Arguments:**\n   577→- `urls`: Array of URLs to extract information from\n   578→- `prompt`: Custom prompt for the LLM extraction\n   579→- `systemPrompt`: System prompt to guide the LLM\n   580→- `schema`: JSON schema for structured data extraction\n   581→- `allowExternalLinks`: Allow extraction from external links\n   582→- `enableWebSearch`: Enable web search for additional context\n   583→- `includeSubdomains`: Include subdomains in extraction\n   584→\n   585→When using a self-hosted instance, the extraction will use your configured LLM. For cloud API, it uses Firecrawl's managed LLM service.\n   586→**Prompt Example:**\n   587→> \"Extract the product name, price, and description from these product pages.\"\n   588→\n   589→**Usage Example:**\n   590→```json\n   591→{\n   592→  \"name\": \"firecrawl_extract\",\n   593→  \"arguments\": {\n   594→    \"urls\": [\"https://example.com/page1\", \"https://example.com/page2\"],\n   595→    \"prompt\": \"Extract product information including name, price, and description\",\n   596→    \"systemPrompt\": \"You are a helpful assistant that extracts product information\",\n   597→    \"schema\": {\n   598→      \"type\": \"object\",\n   599→      \"properties\": {\n   600→        \"name\": { \"type\": \"string\" },\n   601→        \"price\": { \"type\": \"number\" },\n   602→        \"description\": { \"type\": \"string\" }\n   603→      },\n   604→      \"required\": [\"name\", \"price\"]\n   605→    },\n   606→    \"allowExternalLinks\": false,\n   607→    \"enableWebSearch\": false,\n   608→    \"includeSubdomains\": false\n   609→  }\n   610→}\n   611→```\n   612→\n   613→**Returns:**\n   614→- Extracted structured data as defined by your schema\n   615→\n   616→```json\n   617→{\n   618→  \"content\": [\n   619→    {\n   620→      \"type\": \"text\",\n   621→      \"text\": {\n   622→        \"name\": \"Example Product\",\n   623→        \"price\": 99.99,\n   624→        \"description\": \"This is an example product description\"\n   625→      }\n   626→    }\n   627→  ],\n   628→  \"isError\": false\n   629→}\n   630→```\n   631→\n   632→### 9. Deep Research Tool (`firecrawl_deep_research`)\n   633→\n   634→Conduct deep web research on a query using intelligent crawling, search, and LLM analysis.\n   635→\n   636→**Best for:**\n   637→- Complex research questions requiring multiple sources, in-depth analysis.\n   638→\n   639→**Not recommended for:**\n   640→- Simple questions that can be answered with a single search\n   641→- When you need very specific information from a known page (use scrape)\n   642→- When you need results quickly (deep research can take time)\n   643→\n   644→**Arguments:**\n   645→- query (string, required): The research question or topic to explore.\n   646→- maxDepth (number, optional): Maximum recursive depth for crawling/search (default: 3).\n   647→- timeLimit (number, optional): Time limit in seconds for the research session (default: 120).\n   648→- maxUrls (number, optional): Maximum number of URLs to analyze (default: 50).\n   649→\n   650→**Prompt Example:**\n   651→> \"Research the environmental impact of electric vehicles versus gasoline vehicles.\"\n   652→\n   653→**Usage Example:**\n   654→```json\n   655→{\n   656→  \"name\": \"firecrawl_deep_research\",\n   657→  \"arguments\": {\n   658→    \"query\": \"What are the environmental impacts of electric vehicles compared to gasoline vehicles?\",\n   659→    \"maxDepth\": 3,\n   660→    \"timeLimit\": 120,\n   661→    \"maxUrls\": 50\n   662→  }\n   663→}\n   664→```\n   665→\n   666→**Returns:**\n   667→- Final analysis generated by an LLM based on research. (data.finalAnalysis)\n   668→- May also include structured activities and sources used in the research process.\n   669→\n   670→### 10. Generate LLMs.txt Tool (`firecrawl_generate_llmstxt`)\n   671→\n   672→Generate a standardized llms.txt (and optionally llms-full.txt) file for a given domain. This file defines how large language models should interact \n   673→with the site.\n   674→\n   675→**Best for:**\n   676→- Creating machine-readable permission guidelines for AI models.\n   677→\n   678→**Not recommended for:**\n   679→- General content extraction or research\n   680→\n   681→**Arguments:**\n   682→- url (string, required): The base URL of the website to analyze.\n   683→- maxUrls (number, optional): Max number of URLs to include (default: 10).\n   684→- showFullText (boolean, optional): Whether to include llms-full.txt contents in the response.\n   685→\n   686→**Prompt Example:**\n   687→> \"Generate an LLMs.txt file for example.com.\"\n   688→\n   689→**Usage Example:**\n   690→```json\n   691→{\n   692→  \"name\": \"firecrawl_generate_llmstxt\",\n   693→  \"arguments\": {\n   694→    \"url\": \"https://example.com\",\n   695→    \"maxUrls\": 20,\n   696→    \"showFullText\": true\n   697→  }\n   698→}\n   699→```\n   700→\n   701→**Returns:**\n   702→- LLMs.txt file contents (and optionally llms-full.txt)\n   703→\n   704→## Logging System\n   705→\n   706→The server includes comprehensive logging:\n   707→\n   708→- Operation status and progress\n   709→- Performance metrics\n   710→- Credit usage monitoring\n   711→- Rate limit tracking\n   712→- Error conditions\n   713→\n   714→Example log messages:\n   715→\n   716→```\n   717→[INFO] Firecrawl MCP Server initialized successfully\n   718→[INFO] Starting scrape for URL: https://example.com\n   719→[INFO] Batch operation queued with ID: batch_1\n   720→[WARNING] Credit usage has reached warning threshold\n   721→[ERROR] Rate limit exceeded, retrying in 2s...\n   722→```\n   723→\n   724→## Error Handling\n   725→\n   726→The server provides robust error handling:\n   727→\n   728→- Automatic retries for transient errors\n   729→- Rate limit handling with backoff\n   730→- Detailed error messages\n   731→- Credit usage warnings\n   732→- Network resilience\n   733→\n   734→Example error response:\n   735→\n   736→```json\n   737→{\n   738→  \"content\": [\n   739→    {\n   740→      \"type\": \"text\",\n   741→      \"text\": \"Error: Rate limit exceeded. Retrying in 2 seconds...\"\n   742→    }\n   743→  ],\n   744→  \"isError\": true\n   745→}\n   746→```\n   747→\n   748→## Development\n   749→\n   750→```bash\n   751→# Install dependencies\n   752→npm install\n   753→\n   754→# Build\n   755→npm run build\n   756→\n   757→# Run tests\n   758→npm test\n   759→```\n   760→\n   761→### Contributing\n   762→\n   763→1. Fork the repository\n   764→2. Create your feature branch\n   765→3. Run tests: `npm test`\n   766→4. Submit a pull request\n   767→\n   768→### Thanks to contributors\n   769→\n   770→Thanks to [@vrknetha](https://github.com/vrknetha), [@cawstudios](https://caw.tech) for the initial implementation!\n   771→\n   772→Thanks to MCP.so and Klavis AI for hosting and [@gstarwd](https://github.com/gstarwd), [@xiangkaiz](https://github.com/xiangkaiz) and [@zihaolin96](https://github.com/zihaolin96) for integrating our server.\n   773→\n   774→## License\n   775→\n   776→MIT License - see LICENSE file for details\n   777→\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"57e76064-6173-47c1-88f8-65ed08193fcd","timestamp":"2025-07-11T01:36:09.616Z","toolUseResult":{"type":"text","file":{"filePath":"/home/<USER>/development/tools/mcp_servers/firecrawl-mcp-server/README.md","content":"# Firecrawl MCP Server\n\nA Model Context Protocol (MCP) server implementation that integrates with [Firecrawl](https://github.com/mendableai/firecrawl) for web scraping capabilities.\n\n> Big thanks to [@vrknetha](https://github.com/vrknetha), [@knacklabs](https://www.knacklabs.ai) for the initial implementation!\n\n\n## Features\n\n- Web scraping, crawling, and discovery\n- Search and content extraction\n- Deep research and batch scraping\n- Automatic retries and rate limiting\n- Cloud and self-hosted support\n- SSE support\n\n> Play around with [our MCP Server on MCP.so's playground](https://mcp.so/playground?server=firecrawl-mcp-server) or on [Klavis AI](https://www.klavis.ai/mcp-servers).\n\n## Installation\n\n### Running with npx\n\n```bash\nenv FIRECRAWL_API_KEY=fc-YOUR_API_KEY npx -y firecrawl-mcp\n```\n\n### Manual Installation\n\n```bash\nnpm install -g firecrawl-mcp\n```\n\n### Running on Cursor\n\nConfiguring Cursor 🖥️\nNote: Requires Cursor version 0.45.6+\nFor the most up-to-date configuration instructions, please refer to the official Cursor documentation on configuring MCP servers:\n[Cursor MCP Server Configuration Guide](https://docs.cursor.com/context/model-context-protocol#configuring-mcp-servers)\n\nTo configure Firecrawl MCP in Cursor **v0.48.6**\n\n1. Open Cursor Settings\n2. Go to Features > MCP Servers\n3. Click \"+ Add new global MCP server\"\n4. Enter the following code:\n   ```json\n   {\n     \"mcpServers\": {\n       \"firecrawl-mcp\": {\n         \"command\": \"npx\",\n         \"args\": [\"-y\", \"firecrawl-mcp\"],\n         \"env\": {\n           \"FIRECRAWL_API_KEY\": \"YOUR-API-KEY\"\n         }\n       }\n     }\n   }\n   ```\n   \nTo configure Firecrawl MCP in Cursor **v0.45.6**\n\n1. Open Cursor Settings\n2. Go to Features > MCP Servers\n3. Click \"+ Add New MCP Server\"\n4. Enter the following:\n   - Name: \"firecrawl-mcp\" (or your preferred name)\n   - Type: \"command\"\n   - Command: `env FIRECRAWL_API_KEY=your-api-key npx -y firecrawl-mcp`\n\n\n\n> If you are using Windows and are running into issues, try `cmd /c \"set FIRECRAWL_API_KEY=your-api-key && npx -y firecrawl-mcp\"`\n\nReplace `your-api-key` with your Firecrawl API key. If you don't have one yet, you can create an account and get it from https://www.firecrawl.dev/app/api-keys\n\nAfter adding, refresh the MCP server list to see the new tools. The Composer Agent will automatically use Firecrawl MCP when appropriate, but you can explicitly request it by describing your web scraping needs. Access the Composer via Command+L (Mac), select \"Agent\" next to the submit button, and enter your query.\n\n### Running on Windsurf\n\nAdd this to your `./codeium/windsurf/model_config.json`:\n\n```json\n{\n  \"mcpServers\": {\n    \"mcp-server-firecrawl\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"firecrawl-mcp\"],\n      \"env\": {\n        \"FIRECRAWL_API_KEY\": \"YOUR_API_KEY\"\n      }\n    }\n  }\n}\n```\n\n### Running with SSE Local Mode\n\nTo run the server using Server-Sent Events (SSE) locally instead of the default stdio transport:\n\n```bash\nenv SSE_LOCAL=true FIRECRAWL_API_KEY=fc-YOUR_API_KEY npx -y firecrawl-mcp\n```\n\nUse the url: http://localhost:3000/sse\n\n### Installing via Smithery (Legacy)\n\nTo install Firecrawl for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@mendableai/mcp-server-firecrawl):\n\n```bash\nnpx -y @smithery/cli install @mendableai/mcp-server-firecrawl --client claude\n```\n\n### Running on VS Code\n\nFor one-click installation, click one of the install buttons below...\n\n[![Install with NPX in VS Code](https://img.shields.io/badge/VS_Code-NPM-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=firecrawl&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22apiKey%22%2C%22description%22%3A%22Firecrawl%20API%20Key%22%2C%22password%22%3Atrue%7D%5D&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22firecrawl-mcp%22%5D%2C%22env%22%3A%7B%22FIRECRAWL_API_KEY%22%3A%22%24%7Binput%3AapiKey%7D%22%7D%7D) [![Install with NPX in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-NPM-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=firecrawl&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22apiKey%22%2C%22description%22%3A%22Firecrawl%20API%20Key%22%2C%22password%22%3Atrue%7D%5D&config=%7B%22command%22%3A%22npx%22%2C%22args%22%3A%5B%22-y%22%2C%22firecrawl-mcp%22%5D%2C%22env%22%3A%7B%22FIRECRAWL_API_KEY%22%3A%22%24%7Binput%3AapiKey%7D%22%7D%7D&quality=insiders)\n\nFor manual installation, add the following JSON block to your User Settings (JSON) file in VS Code. You can do this by pressing `Ctrl + Shift + P` and typing `Preferences: Open User Settings (JSON)`.\n\n```json\n{\n  \"mcp\": {\n    \"inputs\": [\n      {\n        \"type\": \"promptString\",\n        \"id\": \"apiKey\",\n        \"description\": \"Firecrawl API Key\",\n        \"password\": true\n      }\n    ],\n    \"servers\": {\n      \"firecrawl\": {\n        \"command\": \"npx\",\n        \"args\": [\"-y\", \"firecrawl-mcp\"],\n        \"env\": {\n          \"FIRECRAWL_API_KEY\": \"${input:apiKey}\"\n        }\n      }\n    }\n  }\n}\n```\n\nOptionally, you can add it to a file called `.vscode/mcp.json` in your workspace. This will allow you to share the configuration with others:\n\n```json\n{\n  \"inputs\": [\n    {\n      \"type\": \"promptString\",\n      \"id\": \"apiKey\",\n      \"description\": \"Firecrawl API Key\",\n      \"password\": true\n    }\n  ],\n  \"servers\": {\n    \"firecrawl\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"firecrawl-mcp\"],\n      \"env\": {\n        \"FIRECRAWL_API_KEY\": \"${input:apiKey}\"\n      }\n    }\n  }\n}\n```\n\n## Configuration\n\n### Environment Variables\n\n#### Required for Cloud API\n\n- `FIRECRAWL_API_KEY`: Your Firecrawl API key\n  - Required when using cloud API (default)\n  - Optional when using self-hosted instance with `FIRECRAWL_API_URL`\n- `FIRECRAWL_API_URL` (Optional): Custom API endpoint for self-hosted instances\n  - Example: `https://firecrawl.your-domain.com`\n  - If not provided, the cloud API will be used (requires API key)\n\n#### Optional Configuration\n\n##### Retry Configuration\n\n- `FIRECRAWL_RETRY_MAX_ATTEMPTS`: Maximum number of retry attempts (default: 3)\n- `FIRECRAWL_RETRY_INITIAL_DELAY`: Initial delay in milliseconds before first retry (default: 1000)\n- `FIRECRAWL_RETRY_MAX_DELAY`: Maximum delay in milliseconds between retries (default: 10000)\n- `FIRECRAWL_RETRY_BACKOFF_FACTOR`: Exponential backoff multiplier (default: 2)\n\n##### Credit Usage Monitoring\n\n- `FIRECRAWL_CREDIT_WARNING_THRESHOLD`: Credit usage warning threshold (default: 1000)\n- `FIRECRAWL_CREDIT_CRITICAL_THRESHOLD`: Credit usage critical threshold (default: 100)\n\n### Configuration Examples\n\nFor cloud API usage with custom retry and credit monitoring:\n\n```bash\n# Required for cloud API\nexport FIRECRAWL_API_KEY=your-api-key\n\n# Optional retry configuration\nexport FIRECRAWL_RETRY_MAX_ATTEMPTS=5        # Increase max retry attempts\nexport FIRECRAWL_RETRY_INITIAL_DELAY=2000    # Start with 2s delay\nexport FIRECRAWL_RETRY_MAX_DELAY=30000       # Maximum 30s delay\nexport FIRECRAWL_RETRY_BACKOFF_FACTOR=3      # More aggressive backoff\n\n# Optional credit monitoring\nexport FIRECRAWL_CREDIT_WARNING_THRESHOLD=2000    # Warning at 2000 credits\nexport FIRECRAWL_CREDIT_CRITICAL_THRESHOLD=500    # Critical at 500 credits\n```\n\nFor self-hosted instance:\n\n```bash\n# Required for self-hosted\nexport FIRECRAWL_API_URL=https://firecrawl.your-domain.com\n\n# Optional authentication for self-hosted\nexport FIRECRAWL_API_KEY=your-api-key  # If your instance requires auth\n\n# Custom retry configuration\nexport FIRECRAWL_RETRY_MAX_ATTEMPTS=10\nexport FIRECRAWL_RETRY_INITIAL_DELAY=500     # Start with faster retries\n```\n\n### Usage with Claude Desktop\n\nAdd this to your `claude_desktop_config.json`:\n\n```json\n{\n  \"mcpServers\": {\n    \"mcp-server-firecrawl\": {\n      \"command\": \"npx\",\n      \"args\": [\"-y\", \"firecrawl-mcp\"],\n      \"env\": {\n        \"FIRECRAWL_API_KEY\": \"YOUR_API_KEY_HERE\",\n\n        \"FIRECRAWL_RETRY_MAX_ATTEMPTS\": \"5\",\n        \"FIRECRAWL_RETRY_INITIAL_DELAY\": \"2000\",\n        \"FIRECRAWL_RETRY_MAX_DELAY\": \"30000\",\n        \"FIRECRAWL_RETRY_BACKOFF_FACTOR\": \"3\",\n\n        \"FIRECRAWL_CREDIT_WARNING_THRESHOLD\": \"2000\",\n        \"FIRECRAWL_CREDIT_CRITICAL_THRESHOLD\": \"500\"\n      }\n    }\n  }\n}\n```\n\n### System Configuration\n\nThe server includes several configurable parameters that can be set via environment variables. Here are the default values if not configured:\n\n```typescript\nconst CONFIG = {\n  retry: {\n    maxAttempts: 3, // Number of retry attempts for rate-limited requests\n    initialDelay: 1000, // Initial delay before first retry (in milliseconds)\n    maxDelay: 10000, // Maximum delay between retries (in milliseconds)\n    backoffFactor: 2, // Multiplier for exponential backoff\n  },\n  credit: {\n    warningThreshold: 1000, // Warn when credit usage reaches this level\n    criticalThreshold: 100, // Critical alert when credit usage reaches this level\n  },\n};\n```\n\nThese configurations control:\n\n1. **Retry Behavior**\n\n   - Automatically retries failed requests due to rate limits\n   - Uses exponential backoff to avoid overwhelming the API\n   - Example: With default settings, retries will be attempted at:\n     - 1st retry: 1 second delay\n     - 2nd retry: 2 seconds delay\n     - 3rd retry: 4 seconds delay (capped at maxDelay)\n\n2. **Credit Usage Monitoring**\n   - Tracks API credit consumption for cloud API usage\n   - Provides warnings at specified thresholds\n   - Helps prevent unexpected service interruption\n   - Example: With default settings:\n     - Warning at 1000 credits remaining\n     - Critical alert at 100 credits remaining\n\n### Rate Limiting and Batch Processing\n\nThe server utilizes Firecrawl's built-in rate limiting and batch processing capabilities:\n\n- Automatic rate limit handling with exponential backoff\n- Efficient parallel processing for batch operations\n- Smart request queuing and throttling\n- Automatic retries for transient errors\n\n## How to Choose a Tool\n\nUse this guide to select the right tool for your task:\n\n- **If you know the exact URL(s) you want:**\n  - For one: use **scrape**\n  - For many: use **batch_scrape**\n- **If you need to discover URLs on a site:** use **map**\n- **If you want to search the web for info:** use **search**\n- **If you want to extract structured data:** use **extract**\n- **If you want to analyze a whole site or section:** use **crawl** (with limits!)\n- **If you want to do in-depth research:** use **deep_research**\n- **If you want to generate LLMs.txt:** use **generate_llmstxt**\n\n### Quick Reference Table\n\n| Tool                | Best for                                 | Returns         |\n|---------------------|------------------------------------------|-----------------|\n| scrape              | Single page content                      | markdown/html   |\n| batch_scrape        | Multiple known URLs                      | markdown/html[] |\n| map                 | Discovering URLs on a site               | URL[]           |\n| crawl               | Multi-page extraction (with limits)      | markdown/html[] |\n| search              | Web search for info                      | results[]       |\n| extract             | Structured data from pages               | JSON            |\n| deep_research       | In-depth, multi-source research          | summary, sources|\n| generate_llmstxt    | LLMs.txt for a domain                    | text            |\n\n## Available Tools\n\n### 1. Scrape Tool (`firecrawl_scrape`)\n\nScrape content from a single URL with advanced options.\n\n**Best for:**\n- Single page content extraction, when you know exactly which page contains the information.\n\n**Not recommended for:**\n- Extracting content from multiple pages (use batch_scrape for known URLs, or map + batch_scrape to discover URLs first, or crawl for full page content)\n- When you're unsure which page contains the information (use search)\n- When you need structured data (use extract)\n\n**Common mistakes:**\n- Using scrape for a list of URLs (use batch_scrape instead).\n\n**Prompt Example:**\n> \"Get the content of the page at https://example.com.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_scrape\",\n  \"arguments\": {\n    \"url\": \"https://example.com\",\n    \"formats\": [\"markdown\"],\n    \"onlyMainContent\": true,\n    \"waitFor\": 1000,\n    \"timeout\": 30000,\n    \"mobile\": false,\n    \"includeTags\": [\"article\", \"main\"],\n    \"excludeTags\": [\"nav\", \"footer\"],\n    \"skipTlsVerification\": false\n  }\n}\n```\n\n**Returns:**\n- Markdown, HTML, or other formats as specified.\n\n### 2. Batch Scrape Tool (`firecrawl_batch_scrape`)\n\nScrape multiple URLs efficiently with built-in rate limiting and parallel processing.\n\n**Best for:**\n- Retrieving content from multiple pages, when you know exactly which pages to scrape.\n\n**Not recommended for:**\n- Discovering URLs (use map first if you don't know the URLs)\n- Scraping a single page (use scrape)\n\n**Common mistakes:**\n- Using batch_scrape with too many URLs at once (may hit rate limits or token overflow)\n\n**Prompt Example:**\n> \"Get the content of these three blog posts: [url1, url2, url3].\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_batch_scrape\",\n  \"arguments\": {\n    \"urls\": [\"https://example1.com\", \"https://example2.com\"],\n    \"options\": {\n      \"formats\": [\"markdown\"],\n      \"onlyMainContent\": true\n    }\n  }\n}\n```\n\n**Returns:**\n- Response includes operation ID for status checking:\n\n```json\n{\n  \"content\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Batch operation queued with ID: batch_1. Use firecrawl_check_batch_status to check progress.\"\n    }\n  ],\n  \"isError\": false\n}\n```\n\n### 3. Check Batch Status (`firecrawl_check_batch_status`)\n\nCheck the status of a batch operation.\n\n```json\n{\n  \"name\": \"firecrawl_check_batch_status\",\n  \"arguments\": {\n    \"id\": \"batch_1\"\n  }\n}\n```\n\n### 4. Map Tool (`firecrawl_map`)\n\nMap a website to discover all indexed URLs on the site.\n\n**Best for:**\n- Discovering URLs on a website before deciding what to scrape\n- Finding specific sections of a website\n\n**Not recommended for:**\n- When you already know which specific URL you need (use scrape or batch_scrape)\n- When you need the content of the pages (use scrape after mapping)\n\n**Common mistakes:**\n- Using crawl to discover URLs instead of map\n\n**Prompt Example:**\n> \"List all URLs on example.com.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_map\",\n  \"arguments\": {\n    \"url\": \"https://example.com\"\n  }\n}\n```\n\n**Returns:**\n- Array of URLs found on the site\n\n### 5. Search Tool (`firecrawl_search`)\n\nSearch the web and optionally extract content from search results.\n\n**Best for:**\n- Finding specific information across multiple websites, when you don't know which website has the information.\n- When you need the most relevant content for a query\n\n**Not recommended for:**\n- When you already know which website to scrape (use scrape)\n- When you need comprehensive coverage of a single website (use map or crawl)\n\n**Common mistakes:**\n- Using crawl or map for open-ended questions (use search instead)\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_search\",\n  \"arguments\": {\n    \"query\": \"latest AI research papers 2023\",\n    \"limit\": 5,\n    \"lang\": \"en\",\n    \"country\": \"us\",\n    \"scrapeOptions\": {\n      \"formats\": [\"markdown\"],\n      \"onlyMainContent\": true\n    }\n  }\n}\n```\n\n**Returns:**\n- Array of search results (with optional scraped content)\n\n**Prompt Example:**\n> \"Find the latest research papers on AI published in 2023.\"\n\n### 6. Crawl Tool (`firecrawl_crawl`)\n\nStarts an asynchronous crawl job on a website and extract content from all pages.\n\n**Best for:**\n- Extracting content from multiple related pages, when you need comprehensive coverage.\n\n**Not recommended for:**\n- Extracting content from a single page (use scrape)\n- When token limits are a concern (use map + batch_scrape)\n- When you need fast results (crawling can be slow)\n\n**Warning:** Crawl responses can be very large and may exceed token limits. Limit the crawl depth and number of pages, or use map + batch_scrape for better control.\n\n**Common mistakes:**\n- Setting limit or maxDepth too high (causes token overflow)\n- Using crawl for a single page (use scrape instead)\n\n**Prompt Example:**\n> \"Get all blog posts from the first two levels of example.com/blog.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_crawl\",\n  \"arguments\": {\n    \"url\": \"https://example.com/blog/*\",\n    \"maxDepth\": 2,\n    \"limit\": 100,\n    \"allowExternalLinks\": false,\n    \"deduplicateSimilarURLs\": true\n  }\n}\n```\n\n**Returns:**\n- Response includes operation ID for status checking:\n\n```json\n{\n  \"content\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Started crawl for: https://example.com/* with job ID: 550e8400-e29b-41d4-a716-446655440000. Use firecrawl_check_crawl_status to check progress.\"\n    }\n  ],\n  \"isError\": false\n}\n```\n\n### 7. Check Crawl Status (`firecrawl_check_crawl_status`)\n\nCheck the status of a crawl job.\n\n```json\n{\n  \"name\": \"firecrawl_check_crawl_status\",\n  \"arguments\": {\n    \"id\": \"550e8400-e29b-41d4-a716-446655440000\"\n  }\n}\n```\n\n**Returns:**\n- Response includes the status of the crawl job:\n  \n### 8. Extract Tool (`firecrawl_extract`)\n\nExtract structured information from web pages using LLM capabilities. Supports both cloud AI and self-hosted LLM extraction.\n\n**Best for:**\n- Extracting specific structured data like prices, names, details.\n\n**Not recommended for:**\n- When you need the full content of a page (use scrape)\n- When you're not looking for specific structured data\n\n**Arguments:**\n- `urls`: Array of URLs to extract information from\n- `prompt`: Custom prompt for the LLM extraction\n- `systemPrompt`: System prompt to guide the LLM\n- `schema`: JSON schema for structured data extraction\n- `allowExternalLinks`: Allow extraction from external links\n- `enableWebSearch`: Enable web search for additional context\n- `includeSubdomains`: Include subdomains in extraction\n\nWhen using a self-hosted instance, the extraction will use your configured LLM. For cloud API, it uses Firecrawl's managed LLM service.\n**Prompt Example:**\n> \"Extract the product name, price, and description from these product pages.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_extract\",\n  \"arguments\": {\n    \"urls\": [\"https://example.com/page1\", \"https://example.com/page2\"],\n    \"prompt\": \"Extract product information including name, price, and description\",\n    \"systemPrompt\": \"You are a helpful assistant that extracts product information\",\n    \"schema\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": { \"type\": \"string\" },\n        \"price\": { \"type\": \"number\" },\n        \"description\": { \"type\": \"string\" }\n      },\n      \"required\": [\"name\", \"price\"]\n    },\n    \"allowExternalLinks\": false,\n    \"enableWebSearch\": false,\n    \"includeSubdomains\": false\n  }\n}\n```\n\n**Returns:**\n- Extracted structured data as defined by your schema\n\n```json\n{\n  \"content\": [\n    {\n      \"type\": \"text\",\n      \"text\": {\n        \"name\": \"Example Product\",\n        \"price\": 99.99,\n        \"description\": \"This is an example product description\"\n      }\n    }\n  ],\n  \"isError\": false\n}\n```\n\n### 9. Deep Research Tool (`firecrawl_deep_research`)\n\nConduct deep web research on a query using intelligent crawling, search, and LLM analysis.\n\n**Best for:**\n- Complex research questions requiring multiple sources, in-depth analysis.\n\n**Not recommended for:**\n- Simple questions that can be answered with a single search\n- When you need very specific information from a known page (use scrape)\n- When you need results quickly (deep research can take time)\n\n**Arguments:**\n- query (string, required): The research question or topic to explore.\n- maxDepth (number, optional): Maximum recursive depth for crawling/search (default: 3).\n- timeLimit (number, optional): Time limit in seconds for the research session (default: 120).\n- maxUrls (number, optional): Maximum number of URLs to analyze (default: 50).\n\n**Prompt Example:**\n> \"Research the environmental impact of electric vehicles versus gasoline vehicles.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_deep_research\",\n  \"arguments\": {\n    \"query\": \"What are the environmental impacts of electric vehicles compared to gasoline vehicles?\",\n    \"maxDepth\": 3,\n    \"timeLimit\": 120,\n    \"maxUrls\": 50\n  }\n}\n```\n\n**Returns:**\n- Final analysis generated by an LLM based on research. (data.finalAnalysis)\n- May also include structured activities and sources used in the research process.\n\n### 10. Generate LLMs.txt Tool (`firecrawl_generate_llmstxt`)\n\nGenerate a standardized llms.txt (and optionally llms-full.txt) file for a given domain. This file defines how large language models should interact \nwith the site.\n\n**Best for:**\n- Creating machine-readable permission guidelines for AI models.\n\n**Not recommended for:**\n- General content extraction or research\n\n**Arguments:**\n- url (string, required): The base URL of the website to analyze.\n- maxUrls (number, optional): Max number of URLs to include (default: 10).\n- showFullText (boolean, optional): Whether to include llms-full.txt contents in the response.\n\n**Prompt Example:**\n> \"Generate an LLMs.txt file for example.com.\"\n\n**Usage Example:**\n```json\n{\n  \"name\": \"firecrawl_generate_llmstxt\",\n  \"arguments\": {\n    \"url\": \"https://example.com\",\n    \"maxUrls\": 20,\n    \"showFullText\": true\n  }\n}\n```\n\n**Returns:**\n- LLMs.txt file contents (and optionally llms-full.txt)\n\n## Logging System\n\nThe server includes comprehensive logging:\n\n- Operation status and progress\n- Performance metrics\n- Credit usage monitoring\n- Rate limit tracking\n- Error conditions\n\nExample log messages:\n\n```\n[INFO] Firecrawl MCP Server initialized successfully\n[INFO] Starting scrape for URL: https://example.com\n[INFO] Batch operation queued with ID: batch_1\n[WARNING] Credit usage has reached warning threshold\n[ERROR] Rate limit exceeded, retrying in 2s...\n```\n\n## Error Handling\n\nThe server provides robust error handling:\n\n- Automatic retries for transient errors\n- Rate limit handling with backoff\n- Detailed error messages\n- Credit usage warnings\n- Network resilience\n\nExample error response:\n\n```json\n{\n  \"content\": [\n    {\n      \"type\": \"text\",\n      \"text\": \"Error: Rate limit exceeded. Retrying in 2 seconds...\"\n    }\n  ],\n  \"isError\": true\n}\n```\n\n## Development\n\n```bash\n# Install dependencies\nnpm install\n\n# Build\nnpm run build\n\n# Run tests\nnpm test\n```\n\n### Contributing\n\n1. Fork the repository\n2. Create your feature branch\n3. Run tests: `npm test`\n4. Submit a pull request\n\n### Thanks to contributors\n\nThanks to [@vrknetha](https://github.com/vrknetha), [@cawstudios](https://caw.tech) for the initial implementation!\n\nThanks to MCP.so and Klavis AI for hosting and [@gstarwd](https://github.com/gstarwd), [@xiangkaiz](https://github.com/xiangkaiz) and [@zihaolin96](https://github.com/zihaolin96) for integrating our server.\n\n## License\n\nMIT License - see LICENSE file for details\n","numLines":777,"startLine":1,"totalLines":777}}}
{"parentUuid":"57e76064-6173-47c1-88f8-65ed08193fcd","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01Q1nKjViuqTmNQnWv2FCTBn","type":"tool_result","content":"     1→<h1 align=\"center\">Crawl4AI RAG MCP Server</h1>\n     2→\n     3→<p align=\"center\">\n     4→  <em>Web Crawling and RAG Capabilities for AI Agents and AI Coding Assistants</em>\n     5→</p>\n     6→\n     7→A powerful implementation of the [Model Context Protocol (MCP)](https://modelcontextprotocol.io) integrated with [Crawl4AI](https://crawl4ai.com) and [Supabase](https://supabase.com/) for providing AI agents and AI coding assistants with advanced web crawling and RAG capabilities.\n     8→\n     9→With this MCP server, you can <b>scrape anything</b> and then <b>use that knowledge anywhere</b> for RAG.\n    10→\n    11→The primary goal is to bring this MCP server into [Archon](https://github.com/coleam00/Archon) as I evolve it to be more of a knowledge engine for AI coding assistants to build AI agents. This first version of the Crawl4AI/RAG MCP server will be improved upon greatly soon, especially making it more configurable so you can use different embedding models and run everything locally with Ollama.\n    12→\n    13→Consider this GitHub repository a testbed, hence why I haven't been super actively address issues and pull requests yet. I certainly will though as I bring this into Archon V2!\n    14→\n    15→## Overview\n    16→\n    17→This MCP server provides tools that enable AI agents to crawl websites, store content in a vector database (Supabase), and perform RAG over the crawled content. It follows the best practices for building MCP servers based on the [Mem0 MCP server template](https://github.com/coleam00/mcp-mem0/) I provided on my channel previously.\n    18→\n    19→The server includes several advanced RAG strategies that can be enabled to enhance retrieval quality:\n    20→- **Contextual Embeddings** for enriched semantic understanding\n    21→- **Hybrid Search** combining vector and keyword search\n    22→- **Agentic RAG** for specialized code example extraction\n    23→- **Reranking** for improved result relevance using cross-encoder models\n    24→- **Knowledge Graph** for AI hallucination detection and repository code analysis\n    25→\n    26→See the [Configuration section](#configuration) below for details on how to enable and configure these strategies.\n    27→\n    28→## Vision\n    29→\n    30→The Crawl4AI RAG MCP server is just the beginning. Here's where we're headed:\n    31→\n    32→1. **Integration with Archon**: Building this system directly into [Archon](https://github.com/coleam00/Archon) to create a comprehensive knowledge engine for AI coding assistants to build better AI agents.\n    33→\n    34→2. **Multiple Embedding Models**: Expanding beyond OpenAI to support a variety of embedding models, including the ability to run everything locally with Ollama for complete control and privacy.\n    35→\n    36→3. **Advanced RAG Strategies**: Implementing sophisticated retrieval techniques like contextual retrieval, late chunking, and others to move beyond basic \"naive lookups\" and significantly enhance the power and precision of the RAG system, especially as it integrates with Archon.\n    37→\n    38→4. **Enhanced Chunking Strategy**: Implementing a Context 7-inspired chunking approach that focuses on examples and creates distinct, semantically meaningful sections for each chunk, improving retrieval precision.\n    39→\n    40→5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n    41→\n    42→## Features\n    43→\n    44→- **Smart URL Detection**: Automatically detects and handles different URL types (regular webpages, sitemaps, text files)\n    45→- **Recursive Crawling**: Follows internal links to discover content\n    46→- **Parallel Processing**: Efficiently crawls multiple pages simultaneously\n    47→- **Content Chunking**: Intelligently splits content by headers and size for better processing\n    48→- **Vector Search**: Performs RAG over crawled content, optionally filtering by data source for precision\n    49→- **Source Retrieval**: Retrieve sources available for filtering to guide the RAG process\n    50→\n    51→## Tools\n    52→\n    53→The server provides essential web crawling and search tools:\n    54→\n    55→### Core Tools (Always Available)\n    56→\n    57→1. **`crawl_single_page`**: Quickly crawl a single web page and store its content in the vector database\n    58→2. **`smart_crawl_url`**: Intelligently crawl a full website based on the type of URL provided (sitemap, llms-full.txt, or a regular webpage that needs to be crawled recursively)\n    59→3. **`get_available_sources`**: Get a list of all available sources (domains) in the database\n    60→4. **`perform_rag_query`**: Search for relevant content using semantic search with optional source filtering\n    61→\n    62→### Conditional Tools\n    63→\n    64→5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n    65→\n    66→### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n    67→\n    68→6. **`parse_github_repository`**: Parse a GitHub repository into a Neo4j knowledge graph, extracting classes, methods, functions, and their relationships for hallucination detection\n    69→7. **`check_ai_script_hallucinations`**: Analyze Python scripts for AI hallucinations by validating imports, method calls, and class usage against the knowledge graph\n    70→8. **`query_knowledge_graph`**: Explore and query the Neo4j knowledge graph with commands like `repos`, `classes`, `methods`, and custom Cypher queries\n    71→\n    72→## Prerequisites\n    73→\n    74→- [Docker/Docker Desktop](https://www.docker.com/products/docker-desktop/) if running the MCP server as a container (recommended)\n    75→- [Python 3.12+](https://www.python.org/downloads/) if running the MCP server directly through uv\n    76→- [Supabase](https://supabase.com/) (database for RAG)\n    77→- [OpenAI API key](https://platform.openai.com/api-keys) (for generating embeddings)\n    78→- [Neo4j](https://neo4j.com/) (optional, for knowledge graph functionality) - see [Knowledge Graph Setup](#knowledge-graph-setup) section\n    79→\n    80→## Installation\n    81→\n    82→### Using Docker (Recommended)\n    83→\n    84→1. Clone this repository:\n    85→   ```bash\n    86→   git clone https://github.com/coleam00/mcp-crawl4ai-rag.git\n    87→   cd mcp-crawl4ai-rag\n    88→   ```\n    89→\n    90→2. Build the Docker image:\n    91→   ```bash\n    92→   docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .\n    93→   ```\n    94→\n    95→3. Create a `.env` file based on the configuration section below\n    96→\n    97→### Using uv directly (no Docker)\n    98→\n    99→1. Clone this repository:\n   100→   ```bash\n   101→   git clone https://github.com/coleam00/mcp-crawl4ai-rag.git\n   102→   cd mcp-crawl4ai-rag\n   103→   ```\n   104→\n   105→2. Install uv if you don't have it:\n   106→   ```bash\n   107→   pip install uv\n   108→   ```\n   109→\n   110→3. Create and activate a virtual environment:\n   111→   ```bash\n   112→   uv venv\n   113→   .venv\\Scripts\\activate\n   114→   # on Mac/Linux: source .venv/bin/activate\n   115→   ```\n   116→\n   117→4. Install dependencies:\n   118→   ```bash\n   119→   uv pip install -e .\n   120→   crawl4ai-setup\n   121→   ```\n   122→\n   123→5. Create a `.env` file based on the configuration section below\n   124→\n   125→## Database Setup\n   126→\n   127→Before running the server, you need to set up the database with the pgvector extension:\n   128→\n   129→1. Go to the SQL Editor in your Supabase dashboard (create a new project first if necessary)\n   130→\n   131→2. Create a new query and paste the contents of `crawled_pages.sql`\n   132→\n   133→3. Run the query to create the necessary tables and functions\n   134→\n   135→## Knowledge Graph Setup (Optional)\n   136→\n   137→To enable AI hallucination detection and repository analysis features, you need to set up Neo4j.\n   138→\n   139→Also, the knowledge graph implementation isn't fully compatible with Docker yet, so I would recommend right now running directly through uv if you want to use the hallucination detection within the MCP server!\n   140→\n   141→For installing Neo4j:\n   142→\n   143→### Local AI Package (Recommended)\n   144→\n   145→The easiest way to get Neo4j running locally is with the [Local AI Package](https://github.com/coleam00/local-ai-packaged) - a curated collection of local AI services including Neo4j:\n   146→\n   147→1. **Clone the Local AI Package**:\n   148→   ```bash\n   149→   git clone https://github.com/coleam00/local-ai-packaged.git\n   150→   cd local-ai-packaged\n   151→   ```\n   152→\n   153→2. **Start Neo4j**:\n   154→   Follow the instructions in the Local AI Package repository to start Neo4j with Docker Compose\n   155→\n   156→3. **Default connection details**:\n   157→   - URI: `bolt://localhost:7687`\n   158→   - Username: `neo4j`\n   159→   - Password: Check the Local AI Package documentation for the default password\n   160→\n   161→### Manual Neo4j Installation\n   162→\n   163→Alternatively, install Neo4j directly:\n   164→\n   165→1. **Install Neo4j Desktop**: Download from [neo4j.com/download](https://neo4j.com/download/)\n   166→\n   167→2. **Create a new database**:\n   168→   - Open Neo4j Desktop\n   169→   - Create a new project and database\n   170→   - Set a password for the `neo4j` user\n   171→   - Start the database\n   172→\n   173→3. **Note your connection details**:\n   174→   - URI: `bolt://localhost:7687` (default)\n   175→   - Username: `neo4j` (default)\n   176→   - Password: Whatever you set during creation\n   177→\n   178→## Configuration\n   179→\n   180→Create a `.env` file in the project root with the following variables:\n   181→\n   182→```\n   183→# MCP Server Configuration\n   184→HOST=0.0.0.0\n   185→PORT=8051\n   186→TRANSPORT=sse\n   187→\n   188→# OpenAI API Configuration\n   189→OPENAI_API_KEY=your_openai_api_key\n   190→\n   191→# LLM for summaries and contextual embeddings\n   192→MODEL_CHOICE=gpt-4.1-nano\n   193→\n   194→# RAG Strategies (set to \"true\" or \"false\", default to \"false\")\n   195→USE_CONTEXTUAL_EMBEDDINGS=false\n   196→USE_HYBRID_SEARCH=false\n   197→USE_AGENTIC_RAG=false\n   198→USE_RERANKING=false\n   199→USE_KNOWLEDGE_GRAPH=false\n   200→\n   201→# Supabase Configuration\n   202→SUPABASE_URL=your_supabase_project_url\n   203→SUPABASE_SERVICE_KEY=your_supabase_service_key\n   204→\n   205→# Neo4j Configuration (required for knowledge graph functionality)\n   206→NEO4J_URI=bolt://localhost:7687\n   207→NEO4J_USER=neo4j\n   208→NEO4J_PASSWORD=your_neo4j_password\n   209→```\n   210→\n   211→### RAG Strategy Options\n   212→\n   213→The Crawl4AI RAG MCP server supports four powerful RAG strategies that can be enabled independently:\n   214→\n   215→#### 1. **USE_CONTEXTUAL_EMBEDDINGS**\n   216→When enabled, this strategy enhances each chunk's embedding with additional context from the entire document. The system passes both the full document and the specific chunk to an LLM (configured via `MODEL_CHOICE`) to generate enriched context that gets embedded alongside the chunk content.\n   217→\n   218→- **When to use**: Enable this when you need high-precision retrieval where context matters, such as technical documentation where terms might have different meanings in different sections.\n   219→- **Trade-offs**: Slower indexing due to LLM calls for each chunk, but significantly better retrieval accuracy.\n   220→- **Cost**: Additional LLM API calls during indexing.\n   221→\n   222→#### 2. **USE_HYBRID_SEARCH**\n   223→Combines traditional keyword search with semantic vector search to provide more comprehensive results. The system performs both searches in parallel and intelligently merges results, prioritizing documents that appear in both result sets.\n   224→\n   225→- **When to use**: Enable this when users might search using specific technical terms, function names, or when exact keyword matches are important alongside semantic understanding.\n   226→- **Trade-offs**: Slightly slower search queries but more robust results, especially for technical content.\n   227→- **Cost**: No additional API costs, just computational overhead.\n   228→\n   229→#### 3. **USE_AGENTIC_RAG**\n   230→Enables specialized code example extraction and storage. When crawling documentation, the system identifies code blocks (≥300 characters), extracts them with surrounding context, generates summaries, and stores them in a separate vector database table specifically designed for code search.\n   231→\n   232→- **When to use**: Essential for AI coding assistants that need to find specific code examples, implementation patterns, or usage examples from documentation.\n   233→- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n   234→- **Cost**: Additional LLM API calls for summarizing each code example.\n   235→- **Benefits**: Provides a dedicated `search_code_examples` tool that AI agents can use to find specific code implementations.\n   236→\n   237→#### 4. **USE_RERANKING**\n   238→Applies cross-encoder reranking to search results after initial retrieval. Uses a lightweight cross-encoder model (`cross-encoder/ms-marco-MiniLM-L-6-v2`) to score each result against the original query, then reorders results by relevance.\n   239→\n   240→- **When to use**: Enable this when search precision is critical and you need the most relevant results at the top. Particularly useful for complex queries where semantic similarity alone might not capture query intent.\n   241→- **Trade-offs**: Adds ~100-200ms to search queries depending on result count, but significantly improves result ordering.\n   242→- **Cost**: No additional API costs - uses a local model that runs on CPU.\n   243→- **Benefits**: Better result relevance, especially for complex queries. Works with both regular RAG search and code example search.\n   244→\n   245→#### 5. **USE_KNOWLEDGE_GRAPH**\n   246→Enables AI hallucination detection and repository analysis using Neo4j knowledge graphs. When enabled, the system can parse GitHub repositories into a graph database and validate AI-generated code against real repository structures. (NOT fully compatible with Docker yet, I'd recommend running through uv)\n   247→\n   248→- **When to use**: Enable this for AI coding assistants that need to validate generated code against real implementations, or when you want to detect when AI models hallucinate non-existent methods, classes, or incorrect usage patterns.\n   249→- **Trade-offs**: Requires Neo4j setup and additional dependencies. Repository parsing can be slow for large codebases, and validation requires repositories to be pre-indexed.\n   250→- **Cost**: No additional API costs for validation, but requires Neo4j infrastructure (can use free local installation or cloud AuraDB).\n   251→- **Benefits**: Provides three powerful tools: `parse_github_repository` for indexing codebases, `check_ai_script_hallucinations` for validating AI-generated code, and `query_knowledge_graph` for exploring indexed repositories.\n   252→\n   253→You can now tell the AI coding assistant to add a Python GitHub repository to the knowledge graph like:\n   254→\n   255→\"Add https://github.com/pydantic/pydantic-ai.git to the knowledge graph\"\n   256→\n   257→Make sure the repo URL ends with .git.\n   258→\n   259→You can also have the AI coding assistant check for hallucinations with scripts it just created, or you can manually run the command:\n   260→\n   261→```\n   262→python knowledge_graphs/ai_hallucination_detector.py [full path to your script to analyze]\n   263→```\n   264→\n   265→### Recommended Configurations\n   266→\n   267→**For general documentation RAG:**\n   268→```\n   269→USE_CONTEXTUAL_EMBEDDINGS=false\n   270→USE_HYBRID_SEARCH=true\n   271→USE_AGENTIC_RAG=false\n   272→USE_RERANKING=true\n   273→```\n   274→\n   275→**For AI coding assistant with code examples:**\n   276→```\n   277→USE_CONTEXTUAL_EMBEDDINGS=true\n   278→USE_HYBRID_SEARCH=true\n   279→USE_AGENTIC_RAG=true\n   280→USE_RERANKING=true\n   281→USE_KNOWLEDGE_GRAPH=false\n   282→```\n   283→\n   284→**For AI coding assistant with hallucination detection:**\n   285→```\n   286→USE_CONTEXTUAL_EMBEDDINGS=true\n   287→USE_HYBRID_SEARCH=true\n   288→USE_AGENTIC_RAG=true\n   289→USE_RERANKING=true\n   290→USE_KNOWLEDGE_GRAPH=true\n   291→```\n   292→\n   293→**For fast, basic RAG:**\n   294→```\n   295→USE_CONTEXTUAL_EMBEDDINGS=false\n   296→USE_HYBRID_SEARCH=true\n   297→USE_AGENTIC_RAG=false\n   298→USE_RERANKING=false\n   299→USE_KNOWLEDGE_GRAPH=false\n   300→```\n   301→\n   302→## Running the Server\n   303→\n   304→### Using Docker\n   305→\n   306→```bash\n   307→docker run --env-file .env -p 8051:8051 mcp/crawl4ai-rag\n   308→```\n   309→\n   310→### Using Python\n   311→\n   312→```bash\n   313→uv run src/crawl4ai_mcp.py\n   314→```\n   315→\n   316→The server will start and listen on the configured host and port.\n   317→\n   318→## Integration with MCP Clients\n   319→\n   320→### SSE Configuration\n   321→\n   322→Once you have the server running with SSE transport, you can connect to it using this configuration:\n   323→\n   324→```json\n   325→{\n   326→  \"mcpServers\": {\n   327→    \"crawl4ai-rag\": {\n   328→      \"transport\": \"sse\",\n   329→      \"url\": \"http://localhost:8051/sse\"\n   330→    }\n   331→  }\n   332→}\n   333→```\n   334→\n   335→> **Note for Windsurf users**: Use `serverUrl` instead of `url` in your configuration:\n   336→> ```json\n   337→> {\n   338→>   \"mcpServers\": {\n   339→>     \"crawl4ai-rag\": {\n   340→>       \"transport\": \"sse\",\n   341→>       \"serverUrl\": \"http://localhost:8051/sse\"\n   342→>     }\n   343→>   }\n   344→> }\n   345→> ```\n   346→>\n   347→> **Note for Docker users**: Use `host.docker.internal` instead of `localhost` if your client is running in a different container. This will apply if you are using this MCP server within n8n!\n   348→\n   349→> **Note for Claude Code users**: \n   350→```\n   351→claude mcp add-json crawl4ai-rag '{\"type\":\"http\",\"url\":\"http://localhost:8051/sse\"}' --scope user\n   352→```\n   353→\n   354→### Stdio Configuration\n   355→\n   356→Add this server to your MCP configuration for Claude Desktop, Windsurf, or any other MCP client:\n   357→\n   358→```json\n   359→{\n   360→  \"mcpServers\": {\n   361→    \"crawl4ai-rag\": {\n   362→      \"command\": \"python\",\n   363→      \"args\": [\"path/to/crawl4ai-mcp/src/crawl4ai_mcp.py\"],\n   364→      \"env\": {\n   365→        \"TRANSPORT\": \"stdio\",\n   366→        \"OPENAI_API_KEY\": \"your_openai_api_key\",\n   367→        \"SUPABASE_URL\": \"your_supabase_url\",\n   368→        \"SUPABASE_SERVICE_KEY\": \"your_supabase_service_key\",\n   369→        \"USE_KNOWLEDGE_GRAPH\": \"false\",\n   370→        \"NEO4J_URI\": \"bolt://localhost:7687\",\n   371→        \"NEO4J_USER\": \"neo4j\",\n   372→        \"NEO4J_PASSWORD\": \"your_neo4j_password\"\n   373→      }\n   374→    }\n   375→  }\n   376→}\n   377→```\n   378→\n   379→### Docker with Stdio Configuration\n   380→\n   381→```json\n   382→{\n   383→  \"mcpServers\": {\n   384→    \"crawl4ai-rag\": {\n   385→      \"command\": \"docker\",\n   386→      \"args\": [\"run\", \"--rm\", \"-i\", \n   387→               \"-e\", \"TRANSPORT\", \n   388→               \"-e\", \"OPENAI_API_KEY\", \n   389→               \"-e\", \"SUPABASE_URL\", \n   390→               \"-e\", \"SUPABASE_SERVICE_KEY\",\n   391→               \"-e\", \"USE_KNOWLEDGE_GRAPH\",\n   392→               \"-e\", \"NEO4J_URI\",\n   393→               \"-e\", \"NEO4J_USER\",\n   394→               \"-e\", \"NEO4J_PASSWORD\",\n   395→               \"mcp/crawl4ai\"],\n   396→      \"env\": {\n   397→        \"TRANSPORT\": \"stdio\",\n   398→        \"OPENAI_API_KEY\": \"your_openai_api_key\",\n   399→        \"SUPABASE_URL\": \"your_supabase_url\",\n   400→        \"SUPABASE_SERVICE_KEY\": \"your_supabase_service_key\",\n   401→        \"USE_KNOWLEDGE_GRAPH\": \"false\",\n   402→        \"NEO4J_URI\": \"bolt://localhost:7687\",\n   403→        \"NEO4J_USER\": \"neo4j\",\n   404→        \"NEO4J_PASSWORD\": \"your_neo4j_password\"\n   405→      }\n   406→    }\n   407→  }\n   408→}\n   409→```\n   410→\n   411→## Knowledge Graph Architecture\n   412→\n   413→The knowledge graph system stores repository code structure in Neo4j with the following components:\n   414→\n   415→### Core Components (`knowledge_graphs/` folder):\n   416→\n   417→- **`parse_repo_into_neo4j.py`**: Clones and analyzes GitHub repositories, extracting Python classes, methods, functions, and imports into Neo4j nodes and relationships\n   418→- **`ai_script_analyzer.py`**: Parses Python scripts using AST to extract imports, class instantiations, method calls, and function usage\n   419→- **`knowledge_graph_validator.py`**: Validates AI-generated code against the knowledge graph to detect hallucinations (non-existent methods, incorrect parameters, etc.)\n   420→- **`hallucination_reporter.py`**: Generates comprehensive reports about detected hallucinations with confidence scores and recommendations\n   421→- **`query_knowledge_graph.py`**: Interactive CLI tool for exploring the knowledge graph (functionality now integrated into MCP tools)\n   422→\n   423→### Knowledge Graph Schema:\n   424→\n   425→The Neo4j database stores code structure as:\n   426→\n   427→**Nodes:**\n   428→- `Repository`: GitHub repositories\n   429→- `File`: Python files within repositories  \n   430→- `Class`: Python classes with methods and attributes\n   431→- `Method`: Class methods with parameter information\n   432→- `Function`: Standalone functions\n   433→- `Attribute`: Class attributes\n   434→\n   435→**Relationships:**\n   436→- `Repository` -[:CONTAINS]-> `File`\n   437→- `File` -[:DEFINES]-> `Class`\n   438→- `File` -[:DEFINES]-> `Function`\n   439→- `Class` -[:HAS_METHOD]-> `Method`\n   440→- `Class` -[:HAS_ATTRIBUTE]-> `Attribute`\n   441→\n   442→### Workflow:\n   443→\n   444→1. **Repository Parsing**: Use `parse_github_repository` tool to clone and analyze open-source repositories\n   445→2. **Code Validation**: Use `check_ai_script_hallucinations` tool to validate AI-generated Python scripts\n   446→3. **Knowledge Exploration**: Use `query_knowledge_graph` tool to explore available repositories, classes, and methods\n   447→\n   448→## Building Your Own Server\n   449→\n   450→This implementation provides a foundation for building more complex MCP servers with web crawling capabilities. To build your own:\n   451→\n   452→1. Add your own tools by creating methods with the `@mcp.tool()` decorator\n   453→2. Create your own lifespan function to add your own dependencies\n   454→3. Modify the `utils.py` file for any helper functions you need\n   455→4. Extend the crawling capabilities by adding more specialized crawlers\n   456→\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"b02dcfc9-0a66-418c-bcae-7c8b10fc679e","timestamp":"2025-07-11T01:36:09.616Z","toolUseResult":{"type":"text","file":{"filePath":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md","content":"<h1 align=\"center\">Crawl4AI RAG MCP Server</h1>\n\n<p align=\"center\">\n  <em>Web Crawling and RAG Capabilities for AI Agents and AI Coding Assistants</em>\n</p>\n\nA powerful implementation of the [Model Context Protocol (MCP)](https://modelcontextprotocol.io) integrated with [Crawl4AI](https://crawl4ai.com) and [Supabase](https://supabase.com/) for providing AI agents and AI coding assistants with advanced web crawling and RAG capabilities.\n\nWith this MCP server, you can <b>scrape anything</b> and then <b>use that knowledge anywhere</b> for RAG.\n\nThe primary goal is to bring this MCP server into [Archon](https://github.com/coleam00/Archon) as I evolve it to be more of a knowledge engine for AI coding assistants to build AI agents. This first version of the Crawl4AI/RAG MCP server will be improved upon greatly soon, especially making it more configurable so you can use different embedding models and run everything locally with Ollama.\n\nConsider this GitHub repository a testbed, hence why I haven't been super actively address issues and pull requests yet. I certainly will though as I bring this into Archon V2!\n\n## Overview\n\nThis MCP server provides tools that enable AI agents to crawl websites, store content in a vector database (Supabase), and perform RAG over the crawled content. It follows the best practices for building MCP servers based on the [Mem0 MCP server template](https://github.com/coleam00/mcp-mem0/) I provided on my channel previously.\n\nThe server includes several advanced RAG strategies that can be enabled to enhance retrieval quality:\n- **Contextual Embeddings** for enriched semantic understanding\n- **Hybrid Search** combining vector and keyword search\n- **Agentic RAG** for specialized code example extraction\n- **Reranking** for improved result relevance using cross-encoder models\n- **Knowledge Graph** for AI hallucination detection and repository code analysis\n\nSee the [Configuration section](#configuration) below for details on how to enable and configure these strategies.\n\n## Vision\n\nThe Crawl4AI RAG MCP server is just the beginning. Here's where we're headed:\n\n1. **Integration with Archon**: Building this system directly into [Archon](https://github.com/coleam00/Archon) to create a comprehensive knowledge engine for AI coding assistants to build better AI agents.\n\n2. **Multiple Embedding Models**: Expanding beyond OpenAI to support a variety of embedding models, including the ability to run everything locally with Ollama for complete control and privacy.\n\n3. **Advanced RAG Strategies**: Implementing sophisticated retrieval techniques like contextual retrieval, late chunking, and others to move beyond basic \"naive lookups\" and significantly enhance the power and precision of the RAG system, especially as it integrates with Archon.\n\n4. **Enhanced Chunking Strategy**: Implementing a Context 7-inspired chunking approach that focuses on examples and creates distinct, semantically meaningful sections for each chunk, improving retrieval precision.\n\n5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n\n## Features\n\n- **Smart URL Detection**: Automatically detects and handles different URL types (regular webpages, sitemaps, text files)\n- **Recursive Crawling**: Follows internal links to discover content\n- **Parallel Processing**: Efficiently crawls multiple pages simultaneously\n- **Content Chunking**: Intelligently splits content by headers and size for better processing\n- **Vector Search**: Performs RAG over crawled content, optionally filtering by data source for precision\n- **Source Retrieval**: Retrieve sources available for filtering to guide the RAG process\n\n## Tools\n\nThe server provides essential web crawling and search tools:\n\n### Core Tools (Always Available)\n\n1. **`crawl_single_page`**: Quickly crawl a single web page and store its content in the vector database\n2. **`smart_crawl_url`**: Intelligently crawl a full website based on the type of URL provided (sitemap, llms-full.txt, or a regular webpage that needs to be crawled recursively)\n3. **`get_available_sources`**: Get a list of all available sources (domains) in the database\n4. **`perform_rag_query`**: Search for relevant content using semantic search with optional source filtering\n\n### Conditional Tools\n\n5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n\n### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n\n6. **`parse_github_repository`**: Parse a GitHub repository into a Neo4j knowledge graph, extracting classes, methods, functions, and their relationships for hallucination detection\n7. **`check_ai_script_hallucinations`**: Analyze Python scripts for AI hallucinations by validating imports, method calls, and class usage against the knowledge graph\n8. **`query_knowledge_graph`**: Explore and query the Neo4j knowledge graph with commands like `repos`, `classes`, `methods`, and custom Cypher queries\n\n## Prerequisites\n\n- [Docker/Docker Desktop](https://www.docker.com/products/docker-desktop/) if running the MCP server as a container (recommended)\n- [Python 3.12+](https://www.python.org/downloads/) if running the MCP server directly through uv\n- [Supabase](https://supabase.com/) (database for RAG)\n- [OpenAI API key](https://platform.openai.com/api-keys) (for generating embeddings)\n- [Neo4j](https://neo4j.com/) (optional, for knowledge graph functionality) - see [Knowledge Graph Setup](#knowledge-graph-setup) section\n\n## Installation\n\n### Using Docker (Recommended)\n\n1. Clone this repository:\n   ```bash\n   git clone https://github.com/coleam00/mcp-crawl4ai-rag.git\n   cd mcp-crawl4ai-rag\n   ```\n\n2. Build the Docker image:\n   ```bash\n   docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .\n   ```\n\n3. Create a `.env` file based on the configuration section below\n\n### Using uv directly (no Docker)\n\n1. Clone this repository:\n   ```bash\n   git clone https://github.com/coleam00/mcp-crawl4ai-rag.git\n   cd mcp-crawl4ai-rag\n   ```\n\n2. Install uv if you don't have it:\n   ```bash\n   pip install uv\n   ```\n\n3. Create and activate a virtual environment:\n   ```bash\n   uv venv\n   .venv\\Scripts\\activate\n   # on Mac/Linux: source .venv/bin/activate\n   ```\n\n4. Install dependencies:\n   ```bash\n   uv pip install -e .\n   crawl4ai-setup\n   ```\n\n5. Create a `.env` file based on the configuration section below\n\n## Database Setup\n\nBefore running the server, you need to set up the database with the pgvector extension:\n\n1. Go to the SQL Editor in your Supabase dashboard (create a new project first if necessary)\n\n2. Create a new query and paste the contents of `crawled_pages.sql`\n\n3. Run the query to create the necessary tables and functions\n\n## Knowledge Graph Setup (Optional)\n\nTo enable AI hallucination detection and repository analysis features, you need to set up Neo4j.\n\nAlso, the knowledge graph implementation isn't fully compatible with Docker yet, so I would recommend right now running directly through uv if you want to use the hallucination detection within the MCP server!\n\nFor installing Neo4j:\n\n### Local AI Package (Recommended)\n\nThe easiest way to get Neo4j running locally is with the [Local AI Package](https://github.com/coleam00/local-ai-packaged) - a curated collection of local AI services including Neo4j:\n\n1. **Clone the Local AI Package**:\n   ```bash\n   git clone https://github.com/coleam00/local-ai-packaged.git\n   cd local-ai-packaged\n   ```\n\n2. **Start Neo4j**:\n   Follow the instructions in the Local AI Package repository to start Neo4j with Docker Compose\n\n3. **Default connection details**:\n   - URI: `bolt://localhost:7687`\n   - Username: `neo4j`\n   - Password: Check the Local AI Package documentation for the default password\n\n### Manual Neo4j Installation\n\nAlternatively, install Neo4j directly:\n\n1. **Install Neo4j Desktop**: Download from [neo4j.com/download](https://neo4j.com/download/)\n\n2. **Create a new database**:\n   - Open Neo4j Desktop\n   - Create a new project and database\n   - Set a password for the `neo4j` user\n   - Start the database\n\n3. **Note your connection details**:\n   - URI: `bolt://localhost:7687` (default)\n   - Username: `neo4j` (default)\n   - Password: Whatever you set during creation\n\n## Configuration\n\nCreate a `.env` file in the project root with the following variables:\n\n```\n# MCP Server Configuration\nHOST=0.0.0.0\nPORT=8051\nTRANSPORT=sse\n\n# OpenAI API Configuration\nOPENAI_API_KEY=your_openai_api_key\n\n# LLM for summaries and contextual embeddings\nMODEL_CHOICE=gpt-4.1-nano\n\n# RAG Strategies (set to \"true\" or \"false\", default to \"false\")\nUSE_CONTEXTUAL_EMBEDDINGS=false\nUSE_HYBRID_SEARCH=false\nUSE_AGENTIC_RAG=false\nUSE_RERANKING=false\nUSE_KNOWLEDGE_GRAPH=false\n\n# Supabase Configuration\nSUPABASE_URL=your_supabase_project_url\nSUPABASE_SERVICE_KEY=your_supabase_service_key\n\n# Neo4j Configuration (required for knowledge graph functionality)\nNEO4J_URI=bolt://localhost:7687\nNEO4J_USER=neo4j\nNEO4J_PASSWORD=your_neo4j_password\n```\n\n### RAG Strategy Options\n\nThe Crawl4AI RAG MCP server supports four powerful RAG strategies that can be enabled independently:\n\n#### 1. **USE_CONTEXTUAL_EMBEDDINGS**\nWhen enabled, this strategy enhances each chunk's embedding with additional context from the entire document. The system passes both the full document and the specific chunk to an LLM (configured via `MODEL_CHOICE`) to generate enriched context that gets embedded alongside the chunk content.\n\n- **When to use**: Enable this when you need high-precision retrieval where context matters, such as technical documentation where terms might have different meanings in different sections.\n- **Trade-offs**: Slower indexing due to LLM calls for each chunk, but significantly better retrieval accuracy.\n- **Cost**: Additional LLM API calls during indexing.\n\n#### 2. **USE_HYBRID_SEARCH**\nCombines traditional keyword search with semantic vector search to provide more comprehensive results. The system performs both searches in parallel and intelligently merges results, prioritizing documents that appear in both result sets.\n\n- **When to use**: Enable this when users might search using specific technical terms, function names, or when exact keyword matches are important alongside semantic understanding.\n- **Trade-offs**: Slightly slower search queries but more robust results, especially for technical content.\n- **Cost**: No additional API costs, just computational overhead.\n\n#### 3. **USE_AGENTIC_RAG**\nEnables specialized code example extraction and storage. When crawling documentation, the system identifies code blocks (≥300 characters), extracts them with surrounding context, generates summaries, and stores them in a separate vector database table specifically designed for code search.\n\n- **When to use**: Essential for AI coding assistants that need to find specific code examples, implementation patterns, or usage examples from documentation.\n- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n- **Cost**: Additional LLM API calls for summarizing each code example.\n- **Benefits**: Provides a dedicated `search_code_examples` tool that AI agents can use to find specific code implementations.\n\n#### 4. **USE_RERANKING**\nApplies cross-encoder reranking to search results after initial retrieval. Uses a lightweight cross-encoder model (`cross-encoder/ms-marco-MiniLM-L-6-v2`) to score each result against the original query, then reorders results by relevance.\n\n- **When to use**: Enable this when search precision is critical and you need the most relevant results at the top. Particularly useful for complex queries where semantic similarity alone might not capture query intent.\n- **Trade-offs**: Adds ~100-200ms to search queries depending on result count, but significantly improves result ordering.\n- **Cost**: No additional API costs - uses a local model that runs on CPU.\n- **Benefits**: Better result relevance, especially for complex queries. Works with both regular RAG search and code example search.\n\n#### 5. **USE_KNOWLEDGE_GRAPH**\nEnables AI hallucination detection and repository analysis using Neo4j knowledge graphs. When enabled, the system can parse GitHub repositories into a graph database and validate AI-generated code against real repository structures. (NOT fully compatible with Docker yet, I'd recommend running through uv)\n\n- **When to use**: Enable this for AI coding assistants that need to validate generated code against real implementations, or when you want to detect when AI models hallucinate non-existent methods, classes, or incorrect usage patterns.\n- **Trade-offs**: Requires Neo4j setup and additional dependencies. Repository parsing can be slow for large codebases, and validation requires repositories to be pre-indexed.\n- **Cost**: No additional API costs for validation, but requires Neo4j infrastructure (can use free local installation or cloud AuraDB).\n- **Benefits**: Provides three powerful tools: `parse_github_repository` for indexing codebases, `check_ai_script_hallucinations` for validating AI-generated code, and `query_knowledge_graph` for exploring indexed repositories.\n\nYou can now tell the AI coding assistant to add a Python GitHub repository to the knowledge graph like:\n\n\"Add https://github.com/pydantic/pydantic-ai.git to the knowledge graph\"\n\nMake sure the repo URL ends with .git.\n\nYou can also have the AI coding assistant check for hallucinations with scripts it just created, or you can manually run the command:\n\n```\npython knowledge_graphs/ai_hallucination_detector.py [full path to your script to analyze]\n```\n\n### Recommended Configurations\n\n**For general documentation RAG:**\n```\nUSE_CONTEXTUAL_EMBEDDINGS=false\nUSE_HYBRID_SEARCH=true\nUSE_AGENTIC_RAG=false\nUSE_RERANKING=true\n```\n\n**For AI coding assistant with code examples:**\n```\nUSE_CONTEXTUAL_EMBEDDINGS=true\nUSE_HYBRID_SEARCH=true\nUSE_AGENTIC_RAG=true\nUSE_RERANKING=true\nUSE_KNOWLEDGE_GRAPH=false\n```\n\n**For AI coding assistant with hallucination detection:**\n```\nUSE_CONTEXTUAL_EMBEDDINGS=true\nUSE_HYBRID_SEARCH=true\nUSE_AGENTIC_RAG=true\nUSE_RERANKING=true\nUSE_KNOWLEDGE_GRAPH=true\n```\n\n**For fast, basic RAG:**\n```\nUSE_CONTEXTUAL_EMBEDDINGS=false\nUSE_HYBRID_SEARCH=true\nUSE_AGENTIC_RAG=false\nUSE_RERANKING=false\nUSE_KNOWLEDGE_GRAPH=false\n```\n\n## Running the Server\n\n### Using Docker\n\n```bash\ndocker run --env-file .env -p 8051:8051 mcp/crawl4ai-rag\n```\n\n### Using Python\n\n```bash\nuv run src/crawl4ai_mcp.py\n```\n\nThe server will start and listen on the configured host and port.\n\n## Integration with MCP Clients\n\n### SSE Configuration\n\nOnce you have the server running with SSE transport, you can connect to it using this configuration:\n\n```json\n{\n  \"mcpServers\": {\n    \"crawl4ai-rag\": {\n      \"transport\": \"sse\",\n      \"url\": \"http://localhost:8051/sse\"\n    }\n  }\n}\n```\n\n> **Note for Windsurf users**: Use `serverUrl` instead of `url` in your configuration:\n> ```json\n> {\n>   \"mcpServers\": {\n>     \"crawl4ai-rag\": {\n>       \"transport\": \"sse\",\n>       \"serverUrl\": \"http://localhost:8051/sse\"\n>     }\n>   }\n> }\n> ```\n>\n> **Note for Docker users**: Use `host.docker.internal` instead of `localhost` if your client is running in a different container. This will apply if you are using this MCP server within n8n!\n\n> **Note for Claude Code users**: \n```\nclaude mcp add-json crawl4ai-rag '{\"type\":\"http\",\"url\":\"http://localhost:8051/sse\"}' --scope user\n```\n\n### Stdio Configuration\n\nAdd this server to your MCP configuration for Claude Desktop, Windsurf, or any other MCP client:\n\n```json\n{\n  \"mcpServers\": {\n    \"crawl4ai-rag\": {\n      \"command\": \"python\",\n      \"args\": [\"path/to/crawl4ai-mcp/src/crawl4ai_mcp.py\"],\n      \"env\": {\n        \"TRANSPORT\": \"stdio\",\n        \"OPENAI_API_KEY\": \"your_openai_api_key\",\n        \"SUPABASE_URL\": \"your_supabase_url\",\n        \"SUPABASE_SERVICE_KEY\": \"your_supabase_service_key\",\n        \"USE_KNOWLEDGE_GRAPH\": \"false\",\n        \"NEO4J_URI\": \"bolt://localhost:7687\",\n        \"NEO4J_USER\": \"neo4j\",\n        \"NEO4J_PASSWORD\": \"your_neo4j_password\"\n      }\n    }\n  }\n}\n```\n\n### Docker with Stdio Configuration\n\n```json\n{\n  \"mcpServers\": {\n    \"crawl4ai-rag\": {\n      \"command\": \"docker\",\n      \"args\": [\"run\", \"--rm\", \"-i\", \n               \"-e\", \"TRANSPORT\", \n               \"-e\", \"OPENAI_API_KEY\", \n               \"-e\", \"SUPABASE_URL\", \n               \"-e\", \"SUPABASE_SERVICE_KEY\",\n               \"-e\", \"USE_KNOWLEDGE_GRAPH\",\n               \"-e\", \"NEO4J_URI\",\n               \"-e\", \"NEO4J_USER\",\n               \"-e\", \"NEO4J_PASSWORD\",\n               \"mcp/crawl4ai\"],\n      \"env\": {\n        \"TRANSPORT\": \"stdio\",\n        \"OPENAI_API_KEY\": \"your_openai_api_key\",\n        \"SUPABASE_URL\": \"your_supabase_url\",\n        \"SUPABASE_SERVICE_KEY\": \"your_supabase_service_key\",\n        \"USE_KNOWLEDGE_GRAPH\": \"false\",\n        \"NEO4J_URI\": \"bolt://localhost:7687\",\n        \"NEO4J_USER\": \"neo4j\",\n        \"NEO4J_PASSWORD\": \"your_neo4j_password\"\n      }\n    }\n  }\n}\n```\n\n## Knowledge Graph Architecture\n\nThe knowledge graph system stores repository code structure in Neo4j with the following components:\n\n### Core Components (`knowledge_graphs/` folder):\n\n- **`parse_repo_into_neo4j.py`**: Clones and analyzes GitHub repositories, extracting Python classes, methods, functions, and imports into Neo4j nodes and relationships\n- **`ai_script_analyzer.py`**: Parses Python scripts using AST to extract imports, class instantiations, method calls, and function usage\n- **`knowledge_graph_validator.py`**: Validates AI-generated code against the knowledge graph to detect hallucinations (non-existent methods, incorrect parameters, etc.)\n- **`hallucination_reporter.py`**: Generates comprehensive reports about detected hallucinations with confidence scores and recommendations\n- **`query_knowledge_graph.py`**: Interactive CLI tool for exploring the knowledge graph (functionality now integrated into MCP tools)\n\n### Knowledge Graph Schema:\n\nThe Neo4j database stores code structure as:\n\n**Nodes:**\n- `Repository`: GitHub repositories\n- `File`: Python files within repositories  \n- `Class`: Python classes with methods and attributes\n- `Method`: Class methods with parameter information\n- `Function`: Standalone functions\n- `Attribute`: Class attributes\n\n**Relationships:**\n- `Repository` -[:CONTAINS]-> `File`\n- `File` -[:DEFINES]-> `Class`\n- `File` -[:DEFINES]-> `Function`\n- `Class` -[:HAS_METHOD]-> `Method`\n- `Class` -[:HAS_ATTRIBUTE]-> `Attribute`\n\n### Workflow:\n\n1. **Repository Parsing**: Use `parse_github_repository` tool to clone and analyze open-source repositories\n2. **Code Validation**: Use `check_ai_script_hallucinations` tool to validate AI-generated Python scripts\n3. **Knowledge Exploration**: Use `query_knowledge_graph` tool to explore available repositories, classes, and methods\n\n## Building Your Own Server\n\nThis implementation provides a foundation for building more complex MCP servers with web crawling capabilities. To build your own:\n\n1. Add your own tools by creating methods with the `@mcp.tool()` decorator\n2. Create your own lifespan function to add your own dependencies\n3. Modify the `utils.py` file for any helper functions you need\n4. Extend the crawling capabilities by adding more specialized crawlers\n","numLines":456,"startLine":1,"totalLines":456}}}
{"parentUuid":"b02dcfc9-0a66-418c-bcae-7c8b10fc679e","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01LWZV5Nqso73zNGour4NAE7","type":"tool_result","content":"     1→{\n     2→  \"name\": \"firecrawl-mcp\",\n     3→  \"version\": \"1.12.0\",\n     4→  \"description\": \"MCP server for Firecrawl web scraping integration. Supports both cloud and self-hosted instances. Features include web scraping, batch processing, structured data extraction, and LLM-powered content analysis.\",\n     5→  \"type\": \"module\",\n     6→  \"bin\": {\n     7→    \"firecrawl-mcp\": \"dist/index.js\"\n     8→  },\n     9→  \"files\": [\n    10→    \"dist\"\n    11→  ],\n    12→  \"publishConfig\": {\n    13→    \"access\": \"public\"\n    14→  },\n    15→  \"scripts\": {\n    16→    \"build\": \"tsc && node -e \\\"require('fs').chmodSync('dist/index.js', '755')\\\"\",\n    17→    \"test\": \"node --experimental-vm-modules node_modules/jest/bin/jest.js\",\n    18→    \"start\": \"node dist/index.js\",\n    19→    \"lint\": \"eslint src/**/*.ts\",\n    20→    \"lint:fix\": \"eslint src/**/*.ts --fix\",\n    21→    \"format\": \"prettier --write .\",\n    22→    \"prepare\": \"npm run build\",\n    23→    \"publish\": \"npm run build && npm publish\"\n    24→  },\n    25→  \"license\": \"MIT\",\n    26→  \"dependencies\": {\n    27→    \"@mendable/firecrawl-js\": \"^1.19.0\",\n    28→    \"@modelcontextprotocol/sdk\": \"^1.4.1\",\n    29→    \"dotenv\": \"^16.4.7\",\n    30→    \"express\": \"^5.1.0\",\n    31→    \"shx\": \"^0.3.4\",\n    32→    \"ws\": \"^8.18.1\"\n    33→  },\n    34→  \"devDependencies\": {\n    35→    \"@jest/globals\": \"^29.7.0\",\n    36→    \"@types/express\": \"^5.0.1\",\n    37→    \"@types/jest\": \"^29.5.14\",\n    38→    \"@types/node\": \"^20.10.5\",\n    39→    \"@typescript-eslint/eslint-plugin\": \"^7.0.0\",\n    40→    \"@typescript-eslint/parser\": \"^7.0.0\",\n    41→    \"eslint\": \"^8.56.0\",\n    42→    \"eslint-config-prettier\": \"^9.1.0\",\n    43→    \"jest\": \"^29.7.0\",\n    44→    \"jest-mock-extended\": \"^4.0.0-beta1\",\n    45→    \"prettier\": \"^3.1.1\",\n    46→    \"ts-jest\": \"^29.1.1\",\n    47→    \"typescript\": \"^5.3.3\"\n    48→  },\n    49→  \"engines\": {\n    50→    \"node\": \">=18.0.0\"\n    51→  },\n    52→  \"keywords\": [\n    53→    \"mcp\",\n    54→    \"firecrawl\",\n    55→    \"web-scraping\",\n    56→    \"crawler\",\n    57→    \"content-extraction\"\n    58→  ],\n    59→  \"repository\": {\n    60→    \"type\": \"git\",\n    61→    \"url\": \"git+https://github.com/mendableai/firecrawl-mcp-server.git\"\n    62→  },\n    63→  \"author\": \"vrknetha\",\n    64→  \"bugs\": {\n    65→    \"url\": \"https://github.com/mendableai/firecrawl-mcp-server/issues\"\n    66→  },\n    67→  \"homepage\": \"https://github.com/mendableai/firecrawl-mcp-server#readme\"\n    68→}\n    69→\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"48fe35ac-00f3-4840-8f82-d735fa747369","timestamp":"2025-07-11T01:36:09.617Z","toolUseResult":{"type":"text","file":{"filePath":"/home/<USER>/development/tools/mcp_servers/firecrawl-mcp-server/package.json","content":"{\n  \"name\": \"firecrawl-mcp\",\n  \"version\": \"1.12.0\",\n  \"description\": \"MCP server for Firecrawl web scraping integration. Supports both cloud and self-hosted instances. Features include web scraping, batch processing, structured data extraction, and LLM-powered content analysis.\",\n  \"type\": \"module\",\n  \"bin\": {\n    \"firecrawl-mcp\": \"dist/index.js\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"scripts\": {\n    \"build\": \"tsc && node -e \\\"require('fs').chmodSync('dist/index.js', '755')\\\"\",\n    \"test\": \"node --experimental-vm-modules node_modules/jest/bin/jest.js\",\n    \"start\": \"node dist/index.js\",\n    \"lint\": \"eslint src/**/*.ts\",\n    \"lint:fix\": \"eslint src/**/*.ts --fix\",\n    \"format\": \"prettier --write .\",\n    \"prepare\": \"npm run build\",\n    \"publish\": \"npm run build && npm publish\"\n  },\n  \"license\": \"MIT\",\n  \"dependencies\": {\n    \"@mendable/firecrawl-js\": \"^1.19.0\",\n    \"@modelcontextprotocol/sdk\": \"^1.4.1\",\n    \"dotenv\": \"^16.4.7\",\n    \"express\": \"^5.1.0\",\n    \"shx\": \"^0.3.4\",\n    \"ws\": \"^8.18.1\"\n  },\n  \"devDependencies\": {\n    \"@jest/globals\": \"^29.7.0\",\n    \"@types/express\": \"^5.0.1\",\n    \"@types/jest\": \"^29.5.14\",\n    \"@types/node\": \"^20.10.5\",\n    \"@typescript-eslint/eslint-plugin\": \"^7.0.0\",\n    \"@typescript-eslint/parser\": \"^7.0.0\",\n    \"eslint\": \"^8.56.0\",\n    \"eslint-config-prettier\": \"^9.1.0\",\n    \"jest\": \"^29.7.0\",\n    \"jest-mock-extended\": \"^4.0.0-beta1\",\n    \"prettier\": \"^3.1.1\",\n    \"ts-jest\": \"^29.1.1\",\n    \"typescript\": \"^5.3.3\"\n  },\n  \"engines\": {\n    \"node\": \">=18.0.0\"\n  },\n  \"keywords\": [\n    \"mcp\",\n    \"firecrawl\",\n    \"web-scraping\",\n    \"crawler\",\n    \"content-extraction\"\n  ],\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/mendableai/firecrawl-mcp-server.git\"\n  },\n  \"author\": \"vrknetha\",\n  \"bugs\": {\n    \"url\": \"https://github.com/mendableai/firecrawl-mcp-server/issues\"\n  },\n  \"homepage\": \"https://github.com/mendableai/firecrawl-mcp-server#readme\"\n}\n","numLines":69,"startLine":1,"totalLines":69}}}
{"parentUuid":"48fe35ac-00f3-4840-8f82-d735fa747369","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01D5fvCHtVnQWh3rveMdNb2Y","type":"tool_result","content":"     1→[project]\n     2→name = \"crawl4ai-mcp\"\n     3→version = \"0.1.0\"\n     4→description = \"MCP server for integrating web crawling and RAG into AI agents and AI coding assistants\"\n     5→readme = \"README.md\"\n     6→requires-python = \">=3.12\"\n     7→dependencies = [\n     8→    \"crawl4ai==0.6.2\",\n     9→    \"mcp==1.7.1\",\n    10→    \"supabase==2.15.1\",\n    11→    \"openai==1.71.0\",\n    12→    \"dotenv==0.9.9\",\n    13→    \"sentence-transformers>=4.1.0\",\n    14→    \"neo4j>=5.28.1\",\n    15→]\n    16→\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"d0749b03-411c-42ee-9352-6fe5f54b8245","timestamp":"2025-07-11T01:36:09.617Z","toolUseResult":{"type":"text","file":{"filePath":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/pyproject.toml","content":"[project]\nname = \"crawl4ai-mcp\"\nversion = \"0.1.0\"\ndescription = \"MCP server for integrating web crawling and RAG into AI agents and AI coding assistants\"\nreadme = \"README.md\"\nrequires-python = \">=3.12\"\ndependencies = [\n    \"crawl4ai==0.6.2\",\n    \"mcp==1.7.1\",\n    \"supabase==2.15.1\",\n    \"openai==1.71.0\",\n    \"dotenv==0.9.9\",\n    \"sentence-transformers>=4.1.0\",\n    \"neo4j>=5.28.1\",\n]\n","numLines":16,"startLine":1,"totalLines":16}}}
{"parentUuid":"d0749b03-411c-42ee-9352-6fe5f54b8245","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01GargBKhacqAxyjaqEBG3jX","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"**mcp-crawl4ai-rag** is better for RAG purposes.\n\n**Key advantages:**\n\n1. **Purpose-built for RAG**: Specifically designed for RAG workflows with vector storage, semantic search, and knowledge graph integration\n2. **Advanced RAG strategies**: Supports contextual embeddings, hybrid search, reranking, and agentic RAG for code examples\n3. **Vector database integration**: Uses Supabase with pgvector for efficient semantic search\n4. **AI hallucination detection**: Knowledge graph validation using Neo4j to verify AI-generated code\n5. **Specialized code extraction**: Dedicated tools for finding code examples in documentation\n6. **Intelligent chunking**: Content split by headers/size for optimal retrieval\n\n**firecrawl-mcp-server** is more focused on web scraping/crawling with limited RAG capabilities - it's better for general content extraction but lacks the sophisticated RAG infrastructure needed for knowledge systems."}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":7,"cache_creation_input_tokens":18172,"cache_read_input_tokens":15699,"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CQzNe8tmtrtfiNb6NHf8V","type":"assistant","uuid":"52ff01d7-080a-443f-9da6-fb806cf15bdf","timestamp":"2025-07-11T01:36:16.302Z"}
{"parentUuid":"52ff01d7-080a-443f-9da6-fb806cf15bdf","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":"@mcp-crawl4ai-rag Does this have UI?"},"uuid":"e8191727-8920-4807-9f3a-90c97bf77598","timestamp":"2025-07-11T01:38:01.120Z"}
{"parentUuid":"e8191727-8920-4807-9f3a-90c97bf77598","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01JXDRPSg1kv1iLojXbjJH2q","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_012XmmbykXQg883hJjGcnCKo","name":"Grep","input":{"pattern":"UI|interface|frontend|web.*interface|dashboard|admin","path":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag","output_mode":"content","-i":true}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":511,"cache_read_input_tokens":33871,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNnMbEX696tMk9irzpq","type":"assistant","uuid":"bb6139bc-03a9-4109-b671-273a50df239b","timestamp":"2025-07-11T01:38:05.901Z"}
{"parentUuid":"bb6139bc-03a9-4109-b671-273a50df239b","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01JXDRPSg1kv1iLojXbjJH2q","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01C2zkK8G3qSNbacFkjNdvtT","name":"Read","input":{"file_path":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py","limit":50}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":511,"cache_read_input_tokens":33871,"output_tokens":5,"service_tier":"standard"}},"requestId":"req_011CQzNnMbEX696tMk9irzpq","type":"assistant","uuid":"fb9af376-b450-4acb-a24a-bf5e4d7bff8a","timestamp":"2025-07-11T01:38:06.896Z"}
{"parentUuid":"fb9af376-b450-4acb-a24a-bf5e4d7bff8a","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01C2zkK8G3qSNbacFkjNdvtT","type":"tool_result","content":"     1→\"\"\"\n     2→MCP server for web crawling with Crawl4AI.\n     3→\n     4→This server provides tools to crawl websites using Crawl4AI, automatically detecting\n     5→the appropriate crawl method based on URL type (sitemap, txt file, or regular webpage).\n     6→Also includes AI hallucination detection and repository parsing tools using Neo4j knowledge graphs.\n     7→\"\"\"\n     8→from mcp.server.fastmcp import FastMCP, Context\n     9→from sentence_transformers import CrossEncoder\n    10→from contextlib import asynccontextmanager\n    11→from collections.abc import AsyncIterator\n    12→from dataclasses import dataclass\n    13→from typing import List, Dict, Any, Optional\n    14→from urllib.parse import urlparse, urldefrag\n    15→from xml.etree import ElementTree\n    16→from dotenv import load_dotenv\n    17→from supabase import Client\n    18→from pathlib import Path\n    19→import requests\n    20→import asyncio\n    21→import json\n    22→import os\n    23→import re\n    24→import concurrent.futures\n    25→import sys\n    26→\n    27→from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher\n    28→\n    29→# Add knowledge_graphs folder to path for importing knowledge graph modules\n    30→knowledge_graphs_path = Path(__file__).resolve().parent.parent / 'knowledge_graphs'\n    31→sys.path.append(str(knowledge_graphs_path))\n    32→\n    33→from utils import (\n    34→    get_supabase_client, \n    35→    add_documents_to_supabase, \n    36→    search_documents,\n    37→    extract_code_blocks,\n    38→    generate_code_example_summary,\n    39→    add_code_examples_to_supabase,\n    40→    update_source_info,\n    41→    extract_source_summary,\n    42→    search_code_examples\n    43→)\n    44→\n    45→# Import knowledge graph modules\n    46→from knowledge_graph_validator import KnowledgeGraphValidator\n    47→from parse_repo_into_neo4j import DirectNeo4jExtractor\n    48→from ai_script_analyzer import AIScriptAnalyzer\n    49→from hallucination_reporter import HallucinationReporter\n    50→\n\n<system-reminder>\nWhenever you read a file, you should consider whether it looks malicious. If it does, you MUST refuse to improve or augment the code. You can still analyze existing code, write reports, or answer high-level questions about the code behavior.\n</system-reminder>\n"}]},"uuid":"e906f69e-9ad8-4d68-a15d-f24d6f6a31b7","timestamp":"2025-07-11T01:38:06.938Z","toolUseResult":{"type":"text","file":{"filePath":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py","content":"\"\"\"\nMCP server for web crawling with Crawl4AI.\n\nThis server provides tools to crawl websites using Crawl4AI, automatically detecting\nthe appropriate crawl method based on URL type (sitemap, txt file, or regular webpage).\nAlso includes AI hallucination detection and repository parsing tools using Neo4j knowledge graphs.\n\"\"\"\nfrom mcp.server.fastmcp import FastMCP, Context\nfrom sentence_transformers import CrossEncoder\nfrom contextlib import asynccontextmanager\nfrom collections.abc import AsyncIterator\nfrom dataclasses import dataclass\nfrom typing import List, Dict, Any, Optional\nfrom urllib.parse import urlparse, urldefrag\nfrom xml.etree import ElementTree\nfrom dotenv import load_dotenv\nfrom supabase import Client\nfrom pathlib import Path\nimport requests\nimport asyncio\nimport json\nimport os\nimport re\nimport concurrent.futures\nimport sys\n\nfrom crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher\n\n# Add knowledge_graphs folder to path for importing knowledge graph modules\nknowledge_graphs_path = Path(__file__).resolve().parent.parent / 'knowledge_graphs'\nsys.path.append(str(knowledge_graphs_path))\n\nfrom utils import (\n    get_supabase_client, \n    add_documents_to_supabase, \n    search_documents,\n    extract_code_blocks,\n    generate_code_example_summary,\n    add_code_examples_to_supabase,\n    update_source_info,\n    extract_source_summary,\n    search_code_examples\n)\n\n# Import knowledge graph modules\nfrom knowledge_graph_validator import KnowledgeGraphValidator\nfrom parse_repo_into_neo4j import DirectNeo4jExtractor\nfrom ai_script_analyzer import AIScriptAnalyzer\nfrom hallucination_reporter import HallucinationReporter\n","numLines":50,"startLine":1,"totalLines":1854}}}
{"parentUuid":"e906f69e-9ad8-4d68-a15d-f24d6f6a31b7","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_012XmmbykXQg883hJjGcnCKo","type":"tool_result","content":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/uv.lock:requires-python = \">=3.12\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/uv.lock:requires-dist = [\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        return {\"valid\": False, \"error\": \"Script path is required\"}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        return {\"valid\": False, \"error\": \"Repository URL is required\"}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    This tool is ideal for quickly retrieving content from a specific URL without following links.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Repository name required. Usage: explore <repo_name>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Class name required. Usage: class <class_name>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Method name required. Usage: method <method_name> [class_name]\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Cypher query required. Usage: query <cypher_query>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/pyproject.toml:requires-python = \">=3.12\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    uuid: str = Field(description=\"The unique identifier for this fact\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    source_node_uuid: Optional[str] = Field(None, description=\"UUID of the source node\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:                uuid=result.uuid,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:                source_node_uuid=result.source_node_uuid if hasattr(result, 'source_node_uuid') else None\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    print(\"Enter 'exit' to quit the program.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        await graphiti_client.build_indices_and_constraints()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        print(\"Graphiti indices built successfully.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        print(\"Continuing with existing indices...\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:            if user_input.lower() in ['exit', 'quit', 'bye', 'goodbye']:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:    print(\"  quit           - Exit\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:            elif command == \"quit\":\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:                print(\"❌ Unknown command. Type 'quit' to exit.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            'time', 'uuid', 'hashlib', 'base64', 'itertools', 'functools', 'operator',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:                'optional': True,  # All kwonly args are optional unless explicitly required\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:                param_info['optional'] = False  # No default = required kwonly arg\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (f:File) REQUIRE f.path IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (c:Class) REQUIRE c.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (m:Method) REQUIRE m.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (f:Function) REQUIRE f.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (a:Attribute) REQUIRE a.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            'node_modules', 'build', 'dist', '.pytest_cache', 'docs',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        # Validate imports first (builds context for other validations)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        required_positional = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        keyword_only_required = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                        keyword_only_required.append(param_name)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                        required_positional.append(param_name)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        min_required_positional = len(required_positional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        max_allowed_positional = len(required_positional) + len(optional_positional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        if provided_positional_count < min_required_positional:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                message=f\"Too few positional arguments: provided {provided_positional_count}, required {min_required_positional}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        all_valid_kwarg_names = set(required_positional + optional_positional + keyword_only_required + keyword_only_optional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        # Check required keyword-only arguments\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        missing_required_kwargs = set(keyword_only_required) - provided_keyword_names\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        if missing_required_kwargs:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                message=f\"Missing required keyword arguments: {list(missing_required_kwargs)}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:            # First pass: collect imports and build import map\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:        \"\"\"Extract import information and build import mapping\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:                'Builder', 'Factory', 'Service', 'Controller', 'Processor'\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py:    \"\"\"Command-line interface for the AI Hallucination Detector\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:The primary goal is to bring this MCP server into [Archon](https://github.com/coleam00/Archon) as I evolve it to be more of a knowledge engine for AI coding assistants to build AI agents. This first version of the Crawl4AI/RAG MCP server will be improved upon greatly soon, especially making it more configurable so you can use different embedding models and run everything locally with Ollama.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:This MCP server provides tools that enable AI agents to crawl websites, store content in a vector database (Supabase), and perform RAG over the crawled content. It follows the best practices for building MCP servers based on the [Mem0 MCP server template](https://github.com/coleam00/mcp-mem0/) I provided on my channel previously.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. **Integration with Archon**: Building this system directly into [Archon](https://github.com/coleam00/Archon) to create a comprehensive knowledge engine for AI coding assistants to build better AI agents.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Source Retrieval**: Retrieve sources available for filtering to guide the RAG process\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. **`crawl_single_page`**: Quickly crawl a single web page and store its content in the vector database\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:## Prerequisites\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:2. Build the Docker image:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:   docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. Go to the SQL Editor in your Supabase dashboard (create a new project first if necessary)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:# Neo4j Configuration (required for knowledge graph functionality)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Trade-offs**: Requires Neo4j setup and additional dependencies. Repository parsing can be slow for large codebases, and validation requires repositories to be pre-indexed.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Cost**: No additional API costs for validation, but requires Neo4j infrastructure (can use free local installation or cloud AuraDB).\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:## Building Your Own Server\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:This implementation provides a foundation for building more complex MCP servers with web crawling capabilities. To build your own:"}]},"uuid":"596181db-fa80-4321-a4f5-eba6024d268e","timestamp":"2025-07-11T01:38:06.956Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/uv.lock:requires-python = \">=3.12\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/uv.lock:requires-dist = [\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        return {\"valid\": False, \"error\": \"Script path is required\"}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        return {\"valid\": False, \"error\": \"Repository URL is required\"}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    This tool is ideal for quickly retrieving content from a specific URL without following links.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Repository name required. Usage: explore <repo_name>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Class name required. Usage: class <class_name>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Method name required. Usage: method <method_name> [class_name]\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        \"error\": \"Cypher query required. Usage: query <cypher_query>\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/pyproject.toml:requires-python = \">=3.12\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    uuid: str = Field(description=\"The unique identifier for this fact\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    source_node_uuid: Optional[str] = Field(None, description=\"UUID of the source node\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:                uuid=result.uuid,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:                source_node_uuid=result.source_node_uuid if hasattr(result, 'source_node_uuid') else None\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:    print(\"Enter 'exit' to quit the program.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        await graphiti_client.build_indices_and_constraints()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        print(\"Graphiti indices built successfully.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:        print(\"Continuing with existing indices...\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/test_script.py:            if user_input.lower() in ['exit', 'quit', 'bye', 'goodbye']:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:    print(\"  quit           - Exit\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:            elif command == \"quit\":\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/query_knowledge_graph.py:                print(\"❌ Unknown command. Type 'quit' to exit.\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            'time', 'uuid', 'hashlib', 'base64', 'itertools', 'functools', 'operator',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:                'optional': True,  # All kwonly args are optional unless explicitly required\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:                param_info['optional'] = False  # No default = required kwonly arg\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (f:File) REQUIRE f.path IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (c:Class) REQUIRE c.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (m:Method) REQUIRE m.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (f:Function) REQUIRE f.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            # await session.run(\"CREATE CONSTRAINT IF NOT EXISTS FOR (a:Attribute) REQUIRE a.full_name IS UNIQUE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            'node_modules', 'build', 'dist', '.pytest_cache', 'docs',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        # Validate imports first (builds context for other validations)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        required_positional = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        keyword_only_required = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                        keyword_only_required.append(param_name)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                        required_positional.append(param_name)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        min_required_positional = len(required_positional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        max_allowed_positional = len(required_positional) + len(optional_positional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        if provided_positional_count < min_required_positional:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                message=f\"Too few positional arguments: provided {provided_positional_count}, required {min_required_positional}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        all_valid_kwarg_names = set(required_positional + optional_positional + keyword_only_required + keyword_only_optional)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        # Check required keyword-only arguments\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        missing_required_kwargs = set(keyword_only_required) - provided_keyword_names\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:        if missing_required_kwargs:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/knowledge_graph_validator.py:                message=f\"Missing required keyword arguments: {list(missing_required_kwargs)}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:            # First pass: collect imports and build import map\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:        \"\"\"Extract import information and build import mapping\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:                'Builder', 'Factory', 'Service', 'Controller', 'Processor'\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py:    \"\"\"Command-line interface for the AI Hallucination Detector\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:The primary goal is to bring this MCP server into [Archon](https://github.com/coleam00/Archon) as I evolve it to be more of a knowledge engine for AI coding assistants to build AI agents. This first version of the Crawl4AI/RAG MCP server will be improved upon greatly soon, especially making it more configurable so you can use different embedding models and run everything locally with Ollama.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:This MCP server provides tools that enable AI agents to crawl websites, store content in a vector database (Supabase), and perform RAG over the crawled content. It follows the best practices for building MCP servers based on the [Mem0 MCP server template](https://github.com/coleam00/mcp-mem0/) I provided on my channel previously.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. **Integration with Archon**: Building this system directly into [Archon](https://github.com/coleam00/Archon) to create a comprehensive knowledge engine for AI coding assistants to build better AI agents.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Source Retrieval**: Retrieve sources available for filtering to guide the RAG process\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. **`crawl_single_page`**: Quickly crawl a single web page and store its content in the vector database\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:## Prerequisites\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:2. Build the Docker image:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:   docker build -t mcp/crawl4ai-rag --build-arg PORT=8051 .\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:1. Go to the SQL Editor in your Supabase dashboard (create a new project first if necessary)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:# Neo4j Configuration (required for knowledge graph functionality)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Trade-offs**: Requires Neo4j setup and additional dependencies. Repository parsing can be slow for large codebases, and validation requires repositories to be pre-indexed.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Cost**: No additional API costs for validation, but requires Neo4j infrastructure (can use free local installation or cloud AuraDB).\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:## Building Your Own Server\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:This implementation provides a foundation for building more complex MCP servers with web crawling capabilities. To build your own:","numLines":67}}
{"parentUuid":"596181db-fa80-4321-a4f5-eba6024d268e","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_018d24E7XzmWPNJeRiUdzyci","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"No, **mcp-crawl4ai-rag does not have a web UI**. \n\nIt's a headless MCP (Model Context Protocol) server that provides:\n\n1. **Command-line tools** for knowledge graph exploration (`query_knowledge_graph.py`)\n2. **MCP protocol integration** for AI clients (Claude, Windsurf, VS Code, etc.)\n3. **SSE/stdio transport** for connecting to AI coding assistants\n\nThe server is designed to be consumed by AI agents and coding assistants rather than having a direct user interface. You interact with it through:\n\n- AI coding assistants (Claude Desktop, Windsurf, VS Code with MCP)\n- Command-line scripts for specific tasks\n- Direct API calls via MCP protocol"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":707,"cache_creation_input_tokens":4314,"cache_read_input_tokens":34382,"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CQzNnnWBX5nLJxsp6BssW","type":"assistant","uuid":"06f1058f-cdc4-421a-a4c0-176f88dc91e1","timestamp":"2025-07-11T01:38:12.474Z"}
{"parentUuid":"06f1058f-cdc4-421a-a4c0-176f88dc91e1","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":"Give me the example use cases."},"uuid":"8341d171-0aaf-4d42-ba05-6a53993b2d7a","timestamp":"2025-07-11T01:39:10.478Z"}
{"parentUuid":"8341d171-0aaf-4d42-ba05-6a53993b2d7a","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01XzznHLLLqgYiB9SbbQ759A","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01XWtP6r4jjiuCiuYhEg97pm","name":"TodoWrite","input":{"todos":[{"content":"Analyze README and source code for use case examples","status":"in_progress","priority":"high","id":"1"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":3,"cache_creation_input_tokens":888,"cache_read_input_tokens":38696,"output_tokens":1,"service_tier":"standard"}},"requestId":"req_011CQzNsU9GfdMXuVa4cToqe","type":"assistant","uuid":"18e6dd39-ca85-4c0a-b4fd-ea64109085a3","timestamp":"2025-07-11T01:39:13.926Z"}
{"parentUuid":"18e6dd39-ca85-4c0a-b4fd-ea64109085a3","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01XWtP6r4jjiuCiuYhEg97pm","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"1dc06450-dd27-47cd-83cd-1c1632427953","timestamp":"2025-07-11T01:39:13.969Z","toolUseResult":{"oldTodos":[],"newTodos":[{"content":"Analyze README and source code for use case examples","status":"in_progress","priority":"high","id":"1"}]}}
{"parentUuid":"1dc06450-dd27-47cd-83cd-1c1632427953","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01KYEfBBrBAGhzFYqbG4oAf6","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01V3wxt9YRbhGL5W9JCghD1i","name":"Grep","input":{"pattern":"example|use case|scenario|workflow","path":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag","output_mode":"content","-i":true,"-C":3}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":5,"cache_creation_input_tokens":207,"cache_read_input_tokens":39584,"output_tokens":23,"service_tier":"standard"}},"requestId":"req_011CQzNsiw1qGYp8UpxqGbTF","type":"assistant","uuid":"59c8e372-d3ba-4a29-b6fc-cd77f7098983","timestamp":"2025-07-11T01:39:18.671Z"}
{"parentUuid":"59c8e372-d3ba-4a29-b6fc-cd77f7098983","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01V3wxt9YRbhGL5W9JCghD1i","type":"tool_result","content":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Drop tables if they exist (to allow rerunning the script)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-drop table if exists crawled_pages;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:drop table if exists code_examples;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-drop table if exists sources;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create the sources table\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  to public\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  using (true);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create the code_examples table\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create table code_examples (\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    id bigserial primary key,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    url varchar not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    chunk_number integer not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    content text not null,  -- The code example content\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    summary text not null,  -- Summary of the code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    metadata jsonb not null default '{}'::jsonb,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    source_id text not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    embedding vector(1536),  -- OpenAI embeddings are 1536 dimensions\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index for better vector similarity search performance\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create index on code_examples using ivfflat (embedding vector_cosine_ops);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index on metadata for faster filtering\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create index idx_code_examples_metadata on code_examples using gin (metadata);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index on source_id for faster filtering\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:CREATE INDEX idx_code_examples_source_id ON code_examples (source_id);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create a function to search for code examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create or replace function match_code_examples (\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  query_embedding vector(1536),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  match_count int default 10,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  filter jsonb DEFAULT '{}'::jsonb,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    summary,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    metadata,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    source_id,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    1 - (code_examples.embedding <=> query_embedding) as similarity\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  from code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  where metadata @> filter\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    AND (source_filter IS NULL OR source_id = source_filter)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  order by code_examples.embedding <=> query_embedding\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  limit match_count;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-end;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-$$;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Enable RLS on the code_examples table\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:alter table code_examples enable row level security;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create a policy that allows anyone to read code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create policy \"Allow public read access to code_examples\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  on code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  for select\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  to public\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  using (true);\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-The server includes several advanced RAG strategies that can be enabled to enhance retrieval quality:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Contextual Embeddings** for enriched semantic understanding\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Hybrid Search** combining vector and keyword search\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Agentic RAG** for specialized code example extraction\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Reranking** for improved result relevance using cross-encoder models\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Knowledge Graph** for AI hallucination detection and repository code analysis\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-3. **Advanced RAG Strategies**: Implementing sophisticated retrieval techniques like contextual retrieval, late chunking, and others to move beyond basic \"naive lookups\" and significantly enhance the power and precision of the RAG system, especially as it integrates with Archon.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:4. **Enhanced Chunking Strategy**: Implementing a Context 7-inspired chunking approach that focuses on examples and creates distinct, semantically meaningful sections for each chunk, improving retrieval precision.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-### Conditional Tools\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Cost**: No additional API costs, just computational overhead.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 3. **USE_AGENTIC_RAG**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:Enables specialized code example extraction and storage. When crawling documentation, the system identifies code blocks (≥300 characters), extracts them with surrounding context, generates summaries, and stores them in a separate vector database table specifically designed for code search.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **When to use**: Essential for AI coding assistants that need to find specific code examples, implementation patterns, or usage examples from documentation.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Cost**: Additional LLM API calls for summarizing each code example.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Benefits**: Provides a dedicated `search_code_examples` tool that AI agents can use to find specific code implementations.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 4. **USE_RERANKING**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-Applies cross-encoder reranking to search results after initial retrieval. Uses a lightweight cross-encoder model (`cross-encoder/ms-marco-MiniLM-L-6-v2`) to score each result against the original query, then reorders results by relevance.\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **When to use**: Enable this when search precision is critical and you need the most relevant results at the top. Particularly useful for complex queries where semantic similarity alone might not capture query intent.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Trade-offs**: Adds ~100-200ms to search queries depending on result count, but significantly improves result ordering.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Cost**: No additional API costs - uses a local model that runs on CPU.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Benefits**: Better result relevance, especially for complex queries. Works with both regular RAG search and code example search.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 5. **USE_KNOWLEDGE_GRAPH**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-Enables AI hallucination detection and repository analysis using Neo4j knowledge graphs. When enabled, the system can parse GitHub repositories into a graph database and validate AI-generated code against real repository structures. (NOT fully compatible with Docker yet, I'd recommend running through uv)\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_RERANKING=true\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-```\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:**For AI coding assistant with code examples:**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-```\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_CONTEXTUAL_EMBEDDINGS=true\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_HYBRID_SEARCH=true\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- `Class` -[:HAS_METHOD]-> `Method`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- `Class` -[:HAS_ATTRIBUTE]-> `Attribute`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:### Workflow:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-1. **Repository Parsing**: Use `parse_github_repository` tool to clone and analyze open-source repositories\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-2. **Code Validation**: Use `check_ai_script_hallucinations` tool to validate AI-generated Python scripts\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    return code_blocks\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:def generate_code_example_summary(code: str, context_before: str, context_after: str) -> str:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    Generate a summary for a code example using its surrounding context.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        code: The code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        context_before: Context before the code\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        context_after: Context after the code\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Returns:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        A summary of what the code example demonstrates\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    model_choice = os.getenv(\"MODEL_CHOICE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{context_before[-500:] if len(context_before) > 500 else context_before}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-</context_before>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:<code_example>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{code[:1500] if len(code) > 1500 else code}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:</code_example>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-<context_after>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{context_after[:500] if len(context_after) > 500 else context_after}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-</context_after>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:Based on the code example and its surrounding context, provide a concise summary (2-3 sentences) that describes what this code example demonstrates and its purpose. Focus on the practical application and key concepts illustrated.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        response = openai.chat.completions.create(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            model=model_choice,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            messages=[\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:                {\"role\": \"system\", \"content\": \"You are a helpful assistant that provides concise code example summaries.\"},\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                {\"role\": \"user\", \"content\": prompt}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            ],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            temperature=0.3,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        return response.choices[0].message.content.strip()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    except Exception as e:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        print(f\"Error generating code example summary: {e}\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        return \"Code example for demonstration purposes.\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:def add_code_examples_to_supabase(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    client: Client,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    urls: List[str],\n/home\n\n... [378 lines truncated] ..."}]},"uuid":"4dd5bc50-a9ca-4600-bd54-6e9f2b6aff81","timestamp":"2025-07-11T01:39:18.719Z","toolUseResult":{"mode":"content","numFiles":0,"filenames":[],"content":"/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Drop tables if they exist (to allow rerunning the script)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-drop table if exists crawled_pages;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:drop table if exists code_examples;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-drop table if exists sources;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create the sources table\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  to public\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  using (true);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create the code_examples table\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create table code_examples (\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    id bigserial primary key,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    url varchar not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    chunk_number integer not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    content text not null,  -- The code example content\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    summary text not null,  -- Summary of the code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    metadata jsonb not null default '{}'::jsonb,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    source_id text not null,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    embedding vector(1536),  -- OpenAI embeddings are 1536 dimensions\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index for better vector similarity search performance\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create index on code_examples using ivfflat (embedding vector_cosine_ops);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index on metadata for faster filtering\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create index idx_code_examples_metadata on code_examples using gin (metadata);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql--- Create an index on source_id for faster filtering\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:CREATE INDEX idx_code_examples_source_id ON code_examples (source_id);\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create a function to search for code examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create or replace function match_code_examples (\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  query_embedding vector(1536),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  match_count int default 10,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  filter jsonb DEFAULT '{}'::jsonb,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    summary,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    metadata,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    source_id,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:    1 - (code_examples.embedding <=> query_embedding) as similarity\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  from code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  where metadata @> filter\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-    AND (source_filter IS NULL OR source_id = source_filter)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  order by code_examples.embedding <=> query_embedding\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  limit match_count;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-end;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-$$;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Enable RLS on the code_examples table\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:alter table code_examples enable row level security;\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:-- Create a policy that allows anyone to read code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:create policy \"Allow public read access to code_examples\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql:  on code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  for select\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  to public\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/crawled_pages.sql-  using (true);\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-The server includes several advanced RAG strategies that can be enabled to enhance retrieval quality:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Contextual Embeddings** for enriched semantic understanding\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Hybrid Search** combining vector and keyword search\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Agentic RAG** for specialized code example extraction\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Reranking** for improved result relevance using cross-encoder models\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Knowledge Graph** for AI hallucination detection and repository code analysis\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-3. **Advanced RAG Strategies**: Implementing sophisticated retrieval techniques like contextual retrieval, late chunking, and others to move beyond basic \"naive lookups\" and significantly enhance the power and precision of the RAG system, especially as it integrates with Archon.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:4. **Enhanced Chunking Strategy**: Implementing a Context 7-inspired chunking approach that focuses on examples and creates distinct, semantically meaningful sections for each chunk, improving retrieval precision.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-5. **Performance Optimization**: Increasing crawling and indexing speed to make it more realistic to \"quickly\" index new documentation to then leverage it within the same prompt in an AI coding assistant.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-### Conditional Tools\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:5. **`search_code_examples`** (requires `USE_AGENTIC_RAG=true`): Search specifically for code examples and their summaries from crawled documentation. This tool provides targeted code snippet retrieval for AI coding assistants.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-### Knowledge Graph Tools (requires `USE_KNOWLEDGE_GRAPH=true`, see below)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Cost**: No additional API costs, just computational overhead.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 3. **USE_AGENTIC_RAG**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:Enables specialized code example extraction and storage. When crawling documentation, the system identifies code blocks (≥300 characters), extracts them with surrounding context, generates summaries, and stores them in a separate vector database table specifically designed for code search.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **When to use**: Essential for AI coding assistants that need to find specific code examples, implementation patterns, or usage examples from documentation.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Trade-offs**: Significantly slower crawling due to code extraction and summarization, requires more storage space.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Cost**: Additional LLM API calls for summarizing each code example.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Benefits**: Provides a dedicated `search_code_examples` tool that AI agents can use to find specific code implementations.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 4. **USE_RERANKING**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-Applies cross-encoder reranking to search results after initial retrieval. Uses a lightweight cross-encoder model (`cross-encoder/ms-marco-MiniLM-L-6-v2`) to score each result against the original query, then reorders results by relevance.\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **When to use**: Enable this when search precision is critical and you need the most relevant results at the top. Particularly useful for complex queries where semantic similarity alone might not capture query intent.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Trade-offs**: Adds ~100-200ms to search queries depending on result count, but significantly improves result ordering.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- **Cost**: No additional API costs - uses a local model that runs on CPU.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:- **Benefits**: Better result relevance, especially for complex queries. Works with both regular RAG search and code example search.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-#### 5. **USE_KNOWLEDGE_GRAPH**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-Enables AI hallucination detection and repository analysis using Neo4j knowledge graphs. When enabled, the system can parse GitHub repositories into a graph database and validate AI-generated code against real repository structures. (NOT fully compatible with Docker yet, I'd recommend running through uv)\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_RERANKING=true\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-```\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:**For AI coding assistant with code examples:**\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-```\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_CONTEXTUAL_EMBEDDINGS=true\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-USE_HYBRID_SEARCH=true\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- `Class` -[:HAS_METHOD]-> `Method`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-- `Class` -[:HAS_ATTRIBUTE]-> `Attribute`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md:### Workflow:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-1. **Repository Parsing**: Use `parse_github_repository` tool to clone and analyze open-source repositories\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/README.md-2. **Code Validation**: Use `check_ai_script_hallucinations` tool to validate AI-generated Python scripts\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    return code_blocks\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:def generate_code_example_summary(code: str, context_before: str, context_after: str) -> str:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    Generate a summary for a code example using its surrounding context.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        code: The code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        context_before: Context before the code\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        context_after: Context after the code\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Returns:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        A summary of what the code example demonstrates\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    model_choice = os.getenv(\"MODEL_CHOICE\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{context_before[-500:] if len(context_before) > 500 else context_before}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-</context_before>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:<code_example>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{code[:1500] if len(code) > 1500 else code}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:</code_example>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-<context_after>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-{context_after[:500] if len(context_after) > 500 else context_after}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-</context_after>\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:Based on the code example and its surrounding context, provide a concise summary (2-3 sentences) that describes what this code example demonstrates and its purpose. Focus on the practical application and key concepts illustrated.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        response = openai.chat.completions.create(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            model=model_choice,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            messages=[\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:                {\"role\": \"system\", \"content\": \"You are a helpful assistant that provides concise code example summaries.\"},\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                {\"role\": \"user\", \"content\": prompt}\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            ],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            temperature=0.3,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        return response.choices[0].message.content.strip()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    except Exception as e:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        print(f\"Error generating code example summary: {e}\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        return \"Code example for demonstration purposes.\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:def add_code_examples_to_supabase(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    client: Client,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    urls: List[str],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    chunk_numbers: List[int],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    code_examples: List[str],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    summaries: List[str],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    metadatas: List[Dict[str, Any]],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    batch_size: int = 20\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    Add code examples to the Supabase code_examples table in batches.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        client: Supabase client\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        urls: List of URLs\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        chunk_numbers: List of chunk numbers\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        code_examples: List of code example contents\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        summaries: List of code example summaries\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        metadatas: List of metadata dictionaries\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        batch_size: Size of each batch for insertion\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    unique_urls = list(set(urls))\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    for url in unique_urls:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:            client.table('code_examples').delete().eq('url', url).execute()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        except Exception as e:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:            print(f\"Error deleting existing code examples for {url}: {e}\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    # Process in batches\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    total_items = len(urls)\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        # Create combined texts for embedding (code + summary)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        for j in range(i, batch_end):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:            combined_text = f\"{code_examples[j]}\\n\\nSummary: {summaries[j]}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            batch_texts.append(combined_text)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        # Create embeddings for the batch\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            batch_data.append({\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                'url': urls[idx],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                'chunk_number': chunk_numbers[idx],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:                'content': code_examples[idx],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                'summary': summaries[idx],\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                'metadata': metadatas[idx],  # Store as JSON object, not string\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                'source_id': source_id,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        for retry in range(max_retries):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:                client.table('code_examples').insert(batch_data).execute()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                # Success - break out of retry loop\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                break\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            except Exception as e:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                    successful_inserts = 0\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                    for record in batch_data:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                        try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:                            client.table('code_examples').insert(record).execute()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                            successful_inserts += 1\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                        except Exception as individual_error:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                            print(f\"Failed to insert individual record for URL {record['url']}: {individual_error}\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                    if successful_inserts > 0:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-                        print(f\"Successfully inserted {successful_inserts}/{len(batch_data)} records individually\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        print(f\"Inserted batch {i//batch_size + 1} of {(total_items + batch_size - 1)//batch_size} code examples\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-def update_source_info(client: Client, source_id: str, summary: str, word_count: int):\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        return default_summary\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:def search_code_examples(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    client: Client, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    query: str, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    match_count: int = 10, \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    source_id: Optional[str] = None\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-) -> List[Dict[str, Any]]:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    Search for code examples in Supabase using vector similarity.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        client: Supabase client\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        source_id: Optional source ID to filter results\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    Returns:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        List of matching code examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    # Create a more descriptive query for better embedding match\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    # Since code examples are embedded with their summaries, we should make the query more descriptive\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    enhanced_query = f\"Code example for {query}\\n\\nSummary: Example code showing {query}\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    # Create embedding for the enhanced query\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    query_embedding = create_embedding(enhanced_query)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:    # Execute the search using the match_code_examples function\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    try:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        # Only include filter parameter if filter_metadata is provided and not empty\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        params = {\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        if source_id:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-            params['source_filter'] = source_id\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        result = client.rpc('match_code_examples', params).execute()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        return result.data\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-    except Exception as e:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py:        print(f\"Error searching code examples: {e}\")\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/utils.py-        return []\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    add_documents_to_supabase, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    search_documents,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    extract_code_blocks,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    generate_code_example_summary,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    add_code_examples_to_supabase,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    update_source_info,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    extract_source_summary,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    search_code_examples\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-# Import knowledge graph modules\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        \"word_count\": len(chunk.split())\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    }\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:def process_code_example(args):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    Process a single code example to generate its summary.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    This function is designed to be used with concurrent.futures.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Args:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        The generated summary\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    code, context_before, context_after = args\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    return generate_code_example_summary(code, context_before, context_after)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/<EMAIL>()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-async def crawl_single_page(ctx: Context, url: str) -> str:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # Add documentation chunks to Supabase (AFTER source exists)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            # Extract and process code examples only if enabled\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            extract_code_examples = os.getenv(\"USE_AGENTIC_RAG\", \"false\") == \"true\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            if extract_code_examples:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                code_blocks = extract_code_blocks(result.markdown)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                if code_blocks:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_urls = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_chunk_numbers = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    code_examples = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_summaries = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_metadatas = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    # Process code examples in parallel\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        # Prepare arguments for parallel processing\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        summary_args = [(block['code'], block['context_before'], block['context_after']) \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                                        for block in code_blocks]\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        # Generate summaries in parallel\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        summaries = list(executor.map(process_code_example, summary_args))\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    # Prepare code example data\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_urls.append(url)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_chunk_numbers.append(i)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        code_examples.append(block['code'])\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_summaries.append(summary)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        # Create metadata for code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_meta = {\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                            \"chunk_index\": i,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                            \"url\": url,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        }\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_metadatas.append(code_meta)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    # Add code examples to Supabase\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    add_code_examples_to_supabase(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        supabase_client, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_urls, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_chunk_numbers, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        code_examples, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_summaries, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_metadatas\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    )\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"success\": True,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"url\": url,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"chunks_stored\": len(chunks),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                \"code_examples_stored\": len(code_blocks) if code_blocks else 0,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"content_length\": len(result.markdown),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"total_word_count\": total_word_count,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \"source_id\": source_id,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        batch_size = 20\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        add_documents_to_supabase(supabase_client, urls, chunk_numbers, contents, metadatas, url_to_full_document, batch_size=batch_size)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        # Extract and process code examples from all documents only if enabled\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        extract_code_examples_enabled = os.getenv(\"USE_AGENTIC_RAG\", \"false\") == \"true\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        if extract_code_examples_enabled:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            all_code_blocks = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            code_urls = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            code_chunk_numbers = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            code_examples = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            code_summaries = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            code_metadatas = []\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                code_blocks = extract_code_blocks(md)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                if code_blocks:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    # Process code examples in parallel\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        # Prepare arguments for parallel processing\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        summary_args = [(block['code'], block['context_before'], block['context_after']) \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                                        for block in code_blocks]\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        # Generate summaries in parallel\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        summaries = list(executor.map(process_code_example, summary_args))\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    # Prepare code example data\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    parsed_url = urlparse(source_url)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    source_id = parsed_url.netloc or parsed_url.path\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    for i, (block, summary) in enumerate(zip(code_blocks, summaries)):\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_urls.append(source_url)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        code_chunk_numbers.append(len(code_examples))  # Use global code example index\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        code_examples.append(block['code'])\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_summaries.append(summary)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                        # Create metadata for code example\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_meta = {\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                            \"chunk_index\": len(code_examples) - 1,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                            \"url\": source_url,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                            \"source\": source_id,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                            \"char_count\": len(block['code']),\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        }\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                        code_metadatas.append(code_meta)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            # Add all code examples to Supabase\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            if code_examples:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                add_code_examples_to_supabase(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    supabase_client, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_urls, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_chunk_numbers, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:                    code_examples, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_summaries, \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    code_metadatas,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                    batch_size=batch_size\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"crawl_type\": crawl_type,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"pages_crawled\": len(crawl_results),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"chunks_stored\": chunk_count,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            \"code_examples_stored\": len(code_examples),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"sources_updated\": len(source_content_map),\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"urls_crawled\": [doc['url'] for doc in crawl_results][:5] + ([\"...\"] if len(crawl_results) > 5 else [])\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        }, indent=2)\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    in the database, along with their summaries and statistics. This is useful for discovering \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    what content is available for querying.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    Always use this tool before calling the RAG query or code example query tool\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    with a specific source filter!\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Args:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        ctx: The MCP server provided context\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        query: The search query\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        source: Optional source domain to filter results (e.g., 'example.com')\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        match_count: Maximum number of results to return (default: 5)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Returns:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        }, indent=2)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/<EMAIL>()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:async def search_code_examples(ctx: Context, query: str, source_id: str = None, match_count: int = 5) -> str:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    Search for code examples relevant to the query.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    This tool searches the vector database for code examples relevant to the query and returns\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    the matching examples with their summaries. Optionally filter by source_id.\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Get the source_id by using the get_available_sources tool before calling this search!\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Use the get_available_sources tool first to see what sources are available for filtering.\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Args:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        ctx: The MCP server provided context\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        query: The search query\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:        source_id: Optional source ID to filter results (e.g., 'example.com')\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        match_count: Maximum number of results to return (default: 5)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    Returns:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        JSON string with the search results\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    # Check if code example extraction is enabled\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    extract_code_examples_enabled = os.getenv(\"USE_AGENTIC_RAG\", \"false\") == \"true\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    if not extract_code_examples_enabled:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        return json.dumps({\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \"success\": False,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            \"error\": \"Code example extraction is disabled. Perform a normal RAG search.\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        }, indent=2)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    try:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # Hybrid search: combine vector and keyword search\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # Import the search function from utils\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            from utils import search_code_examples as search_code_examples_impl\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # 1. Get vector search results (get more to account for filtering)\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            vector_results = search_code_examples_impl(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                client=supabase_client,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                query=query,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                match_count=match_count * 2,  # Get double to have room for filtering\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            )\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # 2. Get keyword search results using ILIKE on both content and summary\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            keyword_query = supabase_client.from_('code_examples')\\\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                .select('id, url, chunk_number, content, summary, metadata, source_id')\\\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                .or_(f'content.ilike.%{query}%,summary.ilike.%{query}%')\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-        else:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            # Standard vector search only\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            from utils import search_code_examples as search_code_examples_impl\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-            \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:            results = search_code_examples_impl(\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                client=supabase_client,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                query=query,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-                match_count=match_count,\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    - `(c:Class)-[:HAS_ATTRIBUTE]->(a:Attribute)`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    - `(f:File)-[:DEFINES]->(func:Function)`\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py:    ## Example Workflow:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    ```\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    1. repos                                    # See what repositories are available\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/src/crawl4ai_mcp.py-    2. explore pydantic-ai                      # Explore a specific repository\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-if __name__ == \"__main__\":\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py:    # Example usage\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-    import sys\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_script_analyzer.py-    if len(sys.argv) != 2:\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-        description=\"Detect AI coding assistant hallucinations in Python scripts\",\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-        formatter_class=argparse.RawDescriptionHelpFormatter,\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-        epilog=\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py:Examples:\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-  # Analyze single script\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-  python ai_hallucination_detector.py script.py\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/ai_hallucination_detector.py-  \n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-        exclude_dirs = {\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-            'tests', 'test', '__pycache__', '.git', 'venv', 'env',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-            'node_modules', 'build', 'dist', '.pytest_cache', 'docs',\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:            'examples', 'example', 'demo', 'benchmark'\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-        }\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-        \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-        for root, dirs, files in os.walk(repo_path):\n--\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-async def main():\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py:    \"\"\"Example usage\"\"\"\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-    load_dotenv()\n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-    \n/home/<USER>/development/tools/mcp_servers/mcp-crawl4ai-rag/knowledge_graphs/parse_repo_into_neo4j.py-    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')","numLines":551}}
{"parentUuid":"4dd5bc50-a9ca-4600-bd54-6e9f2b6aff81","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_01TgSfS4XUBuZ2ZqV6ZhjLNy","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"tool_use","id":"toolu_01CLKpwcaFq6wsMF1cqipCtM","name":"TodoWrite","input":{"todos":[{"content":"Analyze README and source code for use case examples","status":"completed","priority":"high","id":"1"}]}}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":5,"cache_creation_input_tokens":7417,"cache_read_input_tokens":39791,"output_tokens":23,"service_tier":"standard"}},"requestId":"req_011CQzNt5cTxAoP3JLXQeZXj","type":"assistant","uuid":"64790d2b-c9a4-4778-93ec-54a12f9cb7ba","timestamp":"2025-07-11T01:39:22.484Z"}
{"parentUuid":"64790d2b-c9a4-4778-93ec-54a12f9cb7ba","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","type":"user","message":{"role":"user","content":[{"tool_use_id":"toolu_01CLKpwcaFq6wsMF1cqipCtM","type":"tool_result","content":"Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable"}]},"uuid":"ea73eefe-b092-4f4e-a311-6aad819d4738","timestamp":"2025-07-11T01:39:22.538Z","toolUseResult":{"oldTodos":[{"content":"Analyze README and source code for use case examples","status":"in_progress","priority":"high","id":"1"}],"newTodos":[{"content":"Analyze README and source code for use case examples","status":"completed","priority":"high","id":"1"}]}}
{"parentUuid":"ea73eefe-b092-4f4e-a311-6aad819d4738","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/tools/mcp_servers","sessionId":"33694181-ca7d-400e-8015-257a618f868c","version":"1.0.48","message":{"id":"msg_011PeHxY24JHaamkEmmfdngP","type":"message","role":"assistant","model":"claude-sonnet-4-20250514","content":[{"type":"text","text":"## Use Cases for mcp-crawl4ai-rag\n\n### 1. **AI Coding Assistant Enhancement**\n- **Crawl documentation sites** (like FastAPI, React, Django docs)\n- **Extract code examples** automatically with summaries\n- **Search for specific implementations** when coding\n- **Get contextual help** with libraries and frameworks\n\n### 2. **Knowledge Base Creation**\n- **Index company documentation** for internal RAG\n- **Process technical wikis** and knowledge repositories  \n- **Create searchable documentation** from websites\n- **Build enterprise knowledge graphs**\n\n### 3. **AI Hallucination Detection**\n- **Parse GitHub repositories** into knowledge graphs\n- **Validate AI-generated code** against real implementations\n- **Check for non-existent methods/classes** in AI suggestions\n- **Verify import statements** and API usage\n\n### 4. **Research & Analysis**\n- **Academic paper discovery** and content extraction\n- **Technical blog aggregation** with semantic search\n- **Competitive analysis** by crawling competitor docs\n- **Technology trend analysis** from multiple sources\n\n### 5. **Developer Workflow Integration**\n- **Real-time documentation lookup** in IDEs\n- **Context-aware code suggestions** based on crawled examples\n- **API reference validation** during development\n- **Best practices extraction** from open-source projects\n\n### 6. **Content Intelligence**\n- **Website content analysis** for SEO/marketing\n- **Documentation gap identification** \n- **Content structure optimization**\n- **Multi-source content synthesis**\n\n### 7. **Training Data Preparation**\n- **Code example collection** for ML model training\n- **Documentation corpus building** for domain-specific models\n- **Knowledge graph construction** for reasoning systems\n- **Contextual embedding generation** for retrieval systems\n\n### 8. **Quality Assurance**\n- **Documentation accuracy checking** against code repositories\n- **API consistency validation** across multiple sources\n- **Code pattern verification** in large codebases\n- **Integration testing** with real-world examples"}],"stop_reason":null,"stop_sequence":null,"usage":{"input_tokens":4,"cache_creation_input_tokens":205,"cache_read_input_tokens":47208,"output_tokens":462,"service_tier":"standard"}},"requestId":"req_011CQzNtMeNrvCFtBVTPVDJL","type":"assistant","uuid":"5ff3dd05-cda1-4428-9f0b-325e8a959e1c","timestamp":"2025-07-11T01:39:33.070Z"}
