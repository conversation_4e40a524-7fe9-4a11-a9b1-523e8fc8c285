{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects","sessionId":"b66aa58d-c386-4124-9de0-3ba1811e04b7","version":"1.0.43","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"b61f0677-cc06-4e11-a42d-87e5fce58513","timestamp":"2025-07-09T09:30:28.867Z"}
{"parentUuid":"b61f0677-cc06-4e11-a42d-87e5fce58513","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects","sessionId":"b66aa58d-c386-4124-9de0-3ba1811e04b7","version":"1.0.43","type":"user","message":{"role":"user","content":"<command-name>/doctor</command-name>\n            <command-message>doctor</command-message>\n            <command-args></command-args>"},"uuid":"38e2079a-6be8-4b93-a19b-9cb72fcf505a","timestamp":"2025-07-09T09:30:55.113Z"}
{"parentUuid":"38e2079a-6be8-4b93-a19b-9cb72fcf505a","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects","sessionId":"b66aa58d-c386-4124-9de0-3ba1811e04b7","version":"1.0.43","type":"user","message":{"role":"user","content":"<local-command-stdout>(no content)</local-command-stdout>"},"uuid":"3987422c-f174-4848-923c-3cbf9435b767","timestamp":"2025-07-09T09:30:55.113Z"}
