{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects/memory-master","sessionId":"98a9b085-a99b-490b-8be4-c202523fe7b1","version":"1.0.44","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"b0c668d0-76c9-40cc-812c-46fca8212ce5","timestamp":"2025-07-09T10:52:25.412Z"}
{"parentUuid":"b0c668d0-76c9-40cc-812c-46fca8212ce5","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects/memory-master","sessionId":"98a9b085-a99b-490b-8be4-c202523fe7b1","version":"1.0.44","type":"user","message":{"role":"user","content":"<command-name>/mcp</command-name>\n            <command-message>mcp</command-message>\n            <command-args></command-args>"},"uuid":"2ce25d67-e9da-4e72-b7bf-70580d6db991","timestamp":"2025-07-09T10:53:16.713Z"}
{"parentUuid":"2ce25d67-e9da-4e72-b7bf-70580d6db991","isSidechain":false,"userType":"external","cwd":"/home/<USER>/development/projects/memory-master","sessionId":"98a9b085-a99b-490b-8be4-c202523fe7b1","version":"1.0.44","type":"user","message":{"role":"user","content":"<local-command-stdout>(no content)</local-command-stdout>"},"uuid":"a803f368-9326-465a-874c-12d4987327b9","timestamp":"2025-07-09T10:53:16.713Z"}
