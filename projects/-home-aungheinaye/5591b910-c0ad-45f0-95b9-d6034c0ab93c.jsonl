{"parentUuid":null,"isSidechain":false,"userType":"external","cwd":"/home/<USER>","sessionId":"5591b910-c0ad-45f0-95b9-d6034c0ab93c","version":"1.0.43","type":"user","message":{"role":"user","content":"Caveat: The messages below were generated by the user while running local commands. DO NOT respond to these messages or otherwise consider them in your response unless the user explicitly asks you to."},"isMeta":true,"uuid":"91a43344-e10b-4294-96a9-b5b906ed29a8","timestamp":"2025-07-06T11:20:08.735Z"}
{"parentUuid":"91a43344-e10b-4294-96a9-b5b906ed29a8","isSidechain":false,"userType":"external","cwd":"/home/<USER>","sessionId":"5591b910-c0ad-45f0-95b9-d6034c0ab93c","version":"1.0.43","type":"user","message":{"role":"user","content":"<command-name>/model</command-name>\n            <command-message>model</command-message>\n            <command-args></command-args>"},"uuid":"82a0bc72-2ad0-4863-84bd-c5e01d703488","timestamp":"2025-07-06T11:20:29.681Z"}
{"parentUuid":"82a0bc72-2ad0-4863-84bd-c5e01d703488","isSidechain":false,"userType":"external","cwd":"/home/<USER>","sessionId":"5591b910-c0ad-45f0-95b9-d6034c0ab93c","version":"1.0.43","type":"user","message":{"role":"user","content":"<local-command-stdout>Set model to \u001b[1msonnet (claude-sonnet-4-20250514)\u001b[22m</local-command-stdout>"},"uuid":"afb5da9c-840d-4e27-992c-e964af71072b","timestamp":"2025-07-06T11:20:29.681Z"}
