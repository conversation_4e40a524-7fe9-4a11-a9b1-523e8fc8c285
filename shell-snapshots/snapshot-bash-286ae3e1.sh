# Snapshot file
# Unset all aliases to avoid conflicts with functions
unalias -a 2>/dev/null || true
# Functions
eval "$(echo 'Y29tbWFuZF9ub3RfZm91bmRfaGFuZGxlICgpIAp7IAogICAgbG9jYWwgcnVuY25mPTE7CiAgICBs
b2NhbCByZXR2YWw9MTI3OwogICAgW1sgJC0gPT0gKiJpIiogXV0gfHwgcnVuY25mPTA7CiAgICBb
WyAhIC1TIC9ydW4vZGJ1cy9zeXN0ZW1fYnVzX3NvY2tldCBdXSAmJiBydW5jbmY9MDsKICAgIFtb
ICEgLXggJy91c3IvbGliZXhlYy9wYWNrYWdla2l0ZCcgXV0gJiYgcnVuY25mPTA7CiAgICBbWyAt
biAke0NPTVBfQ1dPUkQtfSBdXSAmJiBydW5jbmY9MDsKICAgIFtbICEgLXggJy91c3IvbGliZXhl
Yy9way1jb21tYW5kLW5vdC1mb3VuZCcgXV0gJiYgcnVuY25mPTA7CiAgICBpZiBbICRydW5jbmYg
LWVxIDEgXTsgdGhlbgogICAgICAgICcvdXNyL2xpYmV4ZWMvcGstY29tbWFuZC1ub3QtZm91bmQn
ICIkQCI7CiAgICAgICAgcmV0dmFsPSQ/OwogICAgZWxzZQogICAgICAgIGlmIFtbIC1uICIke0JB
U0hfVkVSU0lPTi19IiBdXTsgdGhlbgogICAgICAgICAgICBwcmludGYgJ2Jhc2g6ICVzJXNcbicg
IiR7MTorJDE6IH0iICIkKGdldHRleHQgUGFja2FnZUtpdCAnY29tbWFuZCBub3QgZm91bmQnKSIg
MT4mMjsKICAgICAgICBmaTsKICAgIGZpOwogICAgcmV0dXJuICRyZXR2YWwKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a2xpYnBhdGhfYXBwZW5kICgpIAp7IAogICAgWyAteiAiJEFXS0xJQlBBVEgiIF0gJiYgQVdL
TElCUEFUSD1gZ2F3ayAnQkVHSU4ge3ByaW50IEVOVklST05bIkFXS0xJQlBBVEgiXX0nYDsKICAg
IGV4cG9ydCBBV0tMSUJQQVRIPSIkQVdLTElCUEFUSDokKiIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a2xpYnBhdGhfZGVmYXVsdCAoKSAKeyAKICAgIHVuc2V0IEFXS0xJQlBBVEg7CiAgICBleHBv
cnQgQVdLTElCUEFUSD1gZ2F3ayAnQkVHSU4ge3ByaW50IEVOVklST05bIkFXS0xJQlBBVEgiXX0n
YAp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a2xpYnBhdGhfcHJlcGVuZCAoKSAKeyAKICAgIFsgLXogIiRBV0tMSUJQQVRIIiBdICYmIEFX
S0xJQlBBVEg9YGdhd2sgJ0JFR0lOIHtwcmludCBFTlZJUk9OWyJBV0tMSUJQQVRIIl19J2A7CiAg
ICBleHBvcnQgQVdLTElCUEFUSD0iJCo6JEFXS0xJQlBBVEgiCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a3BhdGhfYXBwZW5kICgpIAp7IAogICAgWyAteiAiJEFXS1BBVEgiIF0gJiYgQVdLUEFUSD1g
Z2F3ayAnQkVHSU4ge3ByaW50IEVOVklST05bIkFXS1BBVEgiXX0nYDsKICAgIGV4cG9ydCBBV0tQ
QVRIPSIkQVdLUEFUSDokKiIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a3BhdGhfZGVmYXVsdCAoKSAKeyAKICAgIHVuc2V0IEFXS1BBVEg7CiAgICBleHBvcnQgQVdL
UEFUSD1gZ2F3ayAnQkVHSU4ge3ByaW50IEVOVklST05bIkFXS1BBVEgiXX0nYAp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'Z2F3a3BhdGhfcHJlcGVuZCAoKSAKeyAKICAgIFsgLXogIiRBV0tQQVRIIiBdICYmIEFXS1BBVEg9
YGdhd2sgJ0JFR0lOIHtwcmludCBFTlZJUk9OWyJBV0tQQVRIIl19J2A7CiAgICBleHBvcnQgQVdL
UEFUSD0iJCo6JEFXS1BBVEgiCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtICgpIAp7IAogICAgaWYgWyAiJCMiIC1sdCAxIF07IHRoZW4KICAgICAgICBudm0gLS1oZWxw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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2FkZF9pb2pzX3ByZWZpeCAoKSAKeyAKICAgIG52bV9lY2hvICIkKG52bV9pb2pzX3ByZWZp
eCktJChudm1fZW5zdXJlX3ZlcnNpb25fcHJlZml4ICIkKG52bV9zdHJpcF9pb2pzX3ByZWZpeCAi
JHsxLX0iKSIpIgp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2FsaWFzICgpIAp7IAogICAgbG9jYWwgQUxJQVM7CiAgICBBTElBUz0iJHsxLX0iOwogICAg
aWYgWyAteiAiJHtBTElBU30iIF07IHRoZW4KICAgICAgICBudm1fZXJyICdBbiBhbGlhcyBpcyBy
ZXF1aXJlZC4nOwogICAgICAgIHJldHVybiAxOwogICAgZmk7CiAgICBpZiAhIEFMSUFTPSIkKG52
bV9ub3JtYWxpemVfbHRzICIke0FMSUFTfSIpIjsgdGhlbgogICAgICAgIHJldHVybiAkPzsKICAg
IGZpOwogICAgaWYgWyAteiAiJHtBTElBU30iIF07IHRoZW4KICAgICAgICByZXR1cm4gMjsKICAg
IGZpOwogICAgbG9jYWwgTlZNX0FMSUFTX1BBVEg7CiAgICBOVk1fQUxJQVNfUEFUSD0iJChudm1f
YWxpYXNfcGF0aCkvJHtBTElBU30iOwogICAgaWYgWyAhIC1mICIke05WTV9BTElBU19QQVRIfSIg
XTsgdGhlbgogICAgICAgIG52bV9lcnIgJ0FsaWFzIGRvZXMgbm90IGV4aXN0Lic7CiAgICAgICAg
cmV0dXJuIDI7CiAgICBmaTsKICAgIGNvbW1hbmQgYXdrICdORicgIiR7TlZNX0FMSUFTX1BBVEh9
Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2FsaWFzX3BhdGggKCkgCnsgCiAgICBudm1fZWNobyAiJChudm1fdmVyc2lvbl9kaXIgb2xk
KS9hbGlhcyIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2F1dG8gKCkgCnsgCiAgICBsb2NhbCBOVk1fTU9ERTsKICAgIE5WTV9NT0RFPSIkezEtfSI7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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2JpbmFyeV9hdmFpbGFibGUgKCkgCnsgCiAgICBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5f
b3JfZXF1YWxfdG8gIiQobnZtX3N0cmlwX2lvanNfcHJlZml4ICIkezEtfSIpIiB2MC44LjYKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NhY2hlX2RpciAoKSAKeyAKICAgIG52bV9lY2hvICIke05WTV9ESVJ9Ly5jYWNoZSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NkICgpIAp7IAogICAgXGNkICIkQCIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NoYW5nZV9wYXRoICgpIAp7IAogICAgaWYgWyAteiAiJHsxLX0iIF07IHRoZW4KICAgICAg
ICBudm1fZWNobyAiJHszLX0kezItfSI7CiAgICBlbHNlCiAgICAgICAgaWYgISBudm1fZWNobyAi
JHsxLX0iIHwgbnZtX2dyZXAgLXEgIiR7TlZNX0RJUn0vW14vXSokezItfSIgJiYgISBudm1fZWNo
byAiJHsxLX0iIHwgbnZtX2dyZXAgLXEgIiR7TlZNX0RJUn0vdmVyc2lvbnMvW14vXSovW14vXSok
ezItfSI7IHRoZW4KICAgICAgICAgICAgbnZtX2VjaG8gIiR7My19JHsyLX06JHsxLX0iOwogICAg
ICAgIGVsc2UKICAgICAgICAgICAgaWYgbnZtX2VjaG8gIiR7MS19IiB8IG52bV9ncmVwIC1FcSAi
KF58OikoL3VzcigvbG9jYWwpPyk/JHsyLX06Lioke05WTV9ESVJ9L1teL10qJHsyLX0iIHx8IG52
bV9lY2hvICIkezEtfSIgfCBudm1fZ3JlcCAtRXEgIihefDopKC91c3IoL2xvY2FsKT8pPyR7Mi19
Oi4qJHtOVk1fRElSfS92ZXJzaW9ucy9bXi9dKi9bXi9dKiR7Mi19IjsgdGhlbgogICAgICAgICAg
ICAgICAgbnZtX2VjaG8gIiR7My19JHsyLX06JHsxLX0iOwogICAgICAgICAgICBlbHNlCiAgICAg
ICAgICAgICAgICBudm1fZWNobyAiJHsxLX0iIHwgY29tbWFuZCBzZWQgLWUgInMjJHtOVk1fRElS
fS9bXi9dKiR7Mi19W146XSojJHszLX0kezItfSMiIC1lICJzIyR7TlZNX0RJUn0vdmVyc2lvbnMv
W14vXSovW14vXSokezItfVteOl0qIyR7My19JHsyLX0jIjsKICAgICAgICAgICAgZmk7CiAgICAg
ICAgZmk7CiAgICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NoZWNrX2ZpbGVfcGVybWlzc2lvbnMgKCkgCnsgCiAgICBudm1faXNfenNoICYmIHNldG9w
dCBsb2NhbF9vcHRpb25zIG5vbm9tYXRjaDsKICAgIGZvciBGSUxFIGluICIkMSIvKiAiJDEiLy5b
IS5dKiAiJDEiLy4uPyo7CiAgICBkbwogICAgICAgIGlmIFsgLWQgIiRGSUxFIiBdOyB0aGVuCiAg
ICAgICAgICAgIGlmIFsgLW4gIiR7TlZNX0RFQlVHLX0iIF07IHRoZW4KICAgICAgICAgICAgICAg
IG52bV9lcnIgIiR7RklMRX0iOwogICAgICAgICAgICBmaTsKICAgICAgICAgICAgaWYgWyAhIC1M
ICIke0ZJTEV9IiBdICYmICEgbnZtX2NoZWNrX2ZpbGVfcGVybWlzc2lvbnMgIiR7RklMRX0iOyB0
aGVuCiAgICAgICAgICAgICAgICByZXR1cm4gMjsKICAgICAgICAgICAgZmk7CiAgICAgICAgZWxz
ZQogICAgICAgICAgICBpZiBbIC1lICIkRklMRSIgXSAmJiBbICEgLXcgIiRGSUxFIiBdICYmIFsg
ISAtTyAiJEZJTEUiIF07IHRoZW4KICAgICAgICAgICAgICAgIG52bV9lcnIgImZpbGUgaXMgbm90
IHdyaXRhYmxlIG9yIHNlbGYtb3duZWQ6ICQobnZtX3Nhbml0aXplX3BhdGggIiRGSUxFIikiOwog
ICAgICAgICAgICAgICAgcmV0dXJuIDE7CiAgICAgICAgICAgIGZpOwogICAgICAgIGZpOwogICAg
ZG9uZTsKICAgIHJldHVybiAwCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NsYW5nX3ZlcnNpb24gKCkgCnsgCiAgICBjbGFuZyAtLXZlcnNpb24gfCBjb21tYW5kIGF3
ayAneyBpZiAoJDIgPT0gInZlcnNpb24iKSBwcmludCAkMzsgZWxzZSBpZiAoJDMgPT0gInZlcnNp
b24iKSBwcmludCAkNCB9JyB8IGNvbW1hbmQgc2VkICdzLy0uKiQvL2cnCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NvbW1hbmRfaW5mbyAoKSAKeyAKICAgIGxvY2FsIENPTU1BTkQ7CiAgICBsb2NhbCBJTkZP
OwogICAgQ09NTUFORD0iJHsxfSI7CiAgICBpZiB0eXBlICIke0NPTU1BTkR9IiB8IG52bV9ncmVw
IC1xIGhhc2hlZDsgdGhlbgogICAgICAgIElORk89IiQodHlwZSAiJHtDT01NQU5EfSIgfCBjb21t
YW5kIHNlZCAtRSAncy9cKHxcKS8vZycgfCBjb21tYW5kIGF3ayAne3ByaW50ICQ0fScpIjsKICAg
IGVsc2UKICAgICAgICBpZiB0eXBlICIke0NPTU1BTkR9IiB8IG52bV9ncmVwIC1xIGFsaWFzZWQ7
IHRoZW4KICAgICAgICAgICAgSU5GTz0iJCh3aGljaCAiJHtDT01NQU5EfSIpICgkKHR5cGUgIiR7
Q09NTUFORH0iIHwgY29tbWFuZCBhd2sgJ3sgJDE9JDI9JDM9JDQ9IiIgO3ByaW50IH0nIHwgY29t
bWFuZCBzZWQgLWUgJ3MvXlwgKi8vZycgLUVlICJzL1xgfCcvL2ciKSkiOwogICAgICAgIGVsc2UK
ICAgICAgICAgICAgaWYgdHlwZSAiJHtDT01NQU5EfSIgfCBudm1fZ3JlcCAtcSAiXiR7Q09NTUFO
RH0gaXMgYW4gYWxpYXMgZm9yIjsgdGhlbgogICAgICAgICAgICAgICAgSU5GTz0iJCh3aGljaCAi
JHtDT01NQU5EfSIpICgkKHR5cGUgIiR7Q09NTUFORH0iIHwgY29tbWFuZCBhd2sgJ3sgJDE9JDI9
JDM9JDQ9JDU9IiIgO3ByaW50IH0nIHwgY29tbWFuZCBzZWQgJ3MvXlwgKi8vZycpKSI7CiAgICAg
ICAgICAgIGVsc2UKICAgICAgICAgICAgICAgIGlmIHR5cGUgIiR7Q09NTUFORH0iIHwgbnZtX2dy
ZXAgLXEgIl4ke0NPTU1BTkR9IGlzIC8iOyB0aGVuCiAgICAgICAgICAgICAgICAgICAgSU5GTz0i
JCh0eXBlICIke0NPTU1BTkR9IiB8IGNvbW1hbmQgYXdrICd7cHJpbnQgJDN9JykiOwogICAgICAg
ICAgICAgICAgZWxzZQogICAgICAgICAgICAgICAgICAgIElORk89IiQodHlwZSAiJHtDT01NQU5E
fSIpIjsKICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICBmaTsKICAgICAgICBmaTsKICAg
IGZpOwogICAgbnZtX2VjaG8gIiR7SU5GT30iCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NvbXBhcmVfY2hlY2tzdW0gKCkgCnsgCiAgICBsb2NhbCBGSUxFOwogICAgRklMRT0iJHsx
LX0iOwogICAgaWYgWyAteiAiJHtGSUxFfSIgXTsgdGhlbgogICAgICAgIG52bV9lcnIgJ1Byb3Zp
ZGVkIGZpbGUgdG8gY2hlY2tzdW0gaXMgZW1wdHkuJzsKICAgICAgICByZXR1cm4gNDsKICAgIGVs
c2UKICAgICAgICBpZiAhIFsgLWYgIiR7RklMRX0iIF07IHRoZW4KICAgICAgICAgICAgbnZtX2Vy
ciAnUHJvdmlkZWQgZmlsZSB0byBjaGVja3N1bSBkb2VzIG5vdCBleGlzdC4nOwogICAgICAgICAg
ICByZXR1cm4gMzsKICAgICAgICBmaTsKICAgIGZpOwogICAgbG9jYWwgQ09NUFVURURfU1VNOwog
ICAgQ09NUFVURURfU1VNPSIkKG52bV9jb21wdXRlX2NoZWNrc3VtICIke0ZJTEV9IikiOwogICAg
bG9jYWwgQ0hFQ0tTVU07CiAgICBDSEVDS1NVTT0iJHsyLX0iOwogICAgaWYgWyAteiAiJHtDSEVD
S1NVTX0iIF07IHRoZW4KICAgICAgICBudm1fZXJyICdQcm92aWRlZCBjaGVja3N1bSB0byBjb21w
YXJlIHRvIGlzIGVtcHR5Lic7CiAgICAgICAgcmV0dXJuIDI7CiAgICBmaTsKICAgIGlmIFsgLXog
IiR7Q09NUFVURURfU1VNfSIgXTsgdGhlbgogICAgICAgIG52bV9lcnIgIkNvbXB1dGVkIGNoZWNr
c3VtIG9mICcke0ZJTEV9JyBpcyBlbXB0eS4iOwogICAgICAgIG52bV9lcnIgJ1dBUk5JTkc6IENv
bnRpbnVpbmcgKndpdGhvdXQgY2hlY2tzdW0gdmVyaWZpY2F0aW9uKic7CiAgICAgICAgcmV0dXJu
OwogICAgZWxzZQogICAgICAgIGlmIFsgIiR7Q09NUFVURURfU1VNfSIgIT0gIiR7Q0hFQ0tTVU19
IiBdICYmIFsgIiR7Q09NUFVURURfU1VNfSIgIT0gIlxcJHtDSEVDS1NVTX0iIF07IHRoZW4KICAg
ICAgICAgICAgbnZtX2VyciAiQ2hlY2tzdW1zIGRvIG5vdCBtYXRjaDogJyR7Q09NUFVURURfU1VN
fScgZm91bmQsICcke0NIRUNLU1VNfScgZXhwZWN0ZWQuIjsKICAgICAgICAgICAgcmV0dXJuIDE7
CiAgICAgICAgZmk7CiAgICBmaTsKICAgIG52bV9lcnIgJ0NoZWNrc3VtcyBtYXRjaGVkIScKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2NvbXB1dGVfY2hlY2tzdW0gKCkgCnsgCiAgICBsb2NhbCBGSUxFOwogICAgRklMRT0iJHsx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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2N1cmxfbGliel9zdXBwb3J0ICgpIAp7IAogICAgY3VybCAtViAyPiAvZGV2L251bGwgfCBu
dm1fZ3JlcCAiXkZlYXR1cmVzOiIgfCBudm1fZ3JlcCAtcSAibGlieiIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2N1cmxfdXNlX2NvbXByZXNzaW9uICgpIAp7IAogICAgbnZtX2N1cmxfbGliel9zdXBwb3J0
ICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJChudm1fY3VybF92ZXJz
aW9uKSIgNy4yMS4wCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2N1cmxfdmVyc2lvbiAoKSAKeyAKICAgIGN1cmwgLVYgfCBjb21tYW5kIGF3ayAneyBpZiAo
JDEgPT0gImN1cmwiKSBwcmludCAkMiB9JyB8IGNvbW1hbmQgc2VkICdzLy0uKiQvL2cnCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2RpZV9vbl9wcmVmaXggKCkgCnsgCiAgICBsb2NhbCBOVk1fREVMRVRFX1BSRUZJWDsKICAg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==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Rvd25sb2FkICgpIAp7IAogICAgaWYgbnZtX2hhcyAiY3VybCI7IHRoZW4KICAgICAgICBs
b2NhbCBDVVJMX0NPTVBSRVNTRURfRkxBRz0iIjsKICAgICAgICBsb2NhbCBDVVJMX0hFQURFUl9G
TEFHPSIiOwogICAgICAgIGlmIFsgLW4gIiR7TlZNX0FVVEhfSEVBREVSOi19IiBdOyB0aGVuCiAg
ICAgICAgICAgIHNhbml0aXplZF9oZWFkZXI9JChudm1fc2FuaXRpemVfYXV0aF9oZWFkZXIgIiR7
TlZNX0FVVEhfSEVBREVSfSIpOwogICAgICAgICAgICBDVVJMX0hFQURFUl9GTEFHPSItLWhlYWRl
ciBcIkF1dGhvcml6YXRpb246ICR7c2FuaXRpemVkX2hlYWRlcn1cIiI7CiAgICAgICAgZmk7CiAg
ICAgICAgaWYgbnZtX2N1cmxfdXNlX2NvbXByZXNzaW9uOyB0aGVuCiAgICAgICAgICAgIENVUkxf
Q09NUFJFU1NFRF9GTEFHPSItLWNvbXByZXNzZWQiOwogICAgICAgIGZpOwogICAgICAgIGxvY2Fs
IE5WTV9ET1dOTE9BRF9BUkdTOwogICAgICAgIE5WTV9ET1dOTE9BRF9BUkdTPScnOwogICAgICAg
IGZvciBhcmcgaW4gIiRAIjsKICAgICAgICBkbwogICAgICAgICAgICBOVk1fRE9XTkxPQURfQVJH
Uz0iJHtOVk1fRE9XTkxPQURfQVJHU30gXCIkYXJnXCIiOwogICAgICAgIGRvbmU7CiAgICAgICAg
ZXZhbCAiY3VybCAtcSAtLWZhaWwgJHtDVVJMX0NPTVBSRVNTRURfRkxBRzotfSAke0NVUkxfSEVB
REVSX0ZMQUc6LX0gJHtOVk1fRE9XTkxPQURfQVJHU30iOwogICAgZWxzZQogICAgICAgIGlmIG52
bV9oYXMgIndnZXQiOyB0aGVuCiAgICAgICAgICAgIEFSR1M9JChudm1fZWNobyAiJEAiIHwgY29t
bWFuZCBzZWQgIgogICAgICBzLy0tcHJvZ3Jlc3MtYmFyIC8tLXByb2dyZXNzPWJhciAvCiAgICAg
IHMvLS1jb21wcmVzc2VkIC8vCiAgICAgIHMvLS1mYWlsIC8vCiAgICAgIHMvLUwgLy8KICAgICAg
cy8tSSAvLS1zZXJ2ZXItcmVzcG9uc2UgLwogICAgICBzLy1zIC8tcSAvCiAgICAgIHMvLXNTIC8t
bnYgLwogICAgICBzLy1vIC8tTyAvCiAgICAgIHMvLUMgLSAvLWMgLwogICAgIik7CiAgICAgICAg
ICAgIGlmIFsgLW4gIiR7TlZNX0FVVEhfSEVBREVSOi19IiBdOyB0aGVuCiAgICAgICAgICAgICAg
ICBBUkdTPSIke0FSR1N9IC0taGVhZGVyIFwiJHtOVk1fQVVUSF9IRUFERVJ9XCIiOwogICAgICAg
ICAgICBmaTsKICAgICAgICAgICAgZXZhbCB3Z2V0ICRBUkdTOwogICAgICAgIGZpOwogICAgZmkK
fQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Rvd25sb2FkX2FydGlmYWN0ICgpIAp7IAogICAgbG9jYWwgRkxBVk9SOwogICAgY2FzZSAi
JHsxLX0iIGluIAogICAgICAgIG5vZGUgfCBpb2pzKQogICAgICAgICAgICBGTEFWT1I9IiR7MX0i
CiAgICAgICAgOzsKICAgICAgICAqKQogICAgICAgICAgICBudm1fZXJyICdzdXBwb3J0ZWQgZmxh
dm9yczogbm9kZSwgaW9qcyc7CiAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgOzsKICAgIGVz
YWM7CiAgICBsb2NhbCBLSU5EOwogICAgY2FzZSAiJHsyLX0iIGluIAogICAgICAgIGJpbmFyeSB8
IHNvdXJjZSkKICAgICAgICAgICAgS0lORD0iJHsyfSIKICAgICAgICA7OwogICAgICAgICopCiAg
ICAgICAgICAgIG52bV9lcnIgJ3N1cHBvcnRlZCBraW5kczogYmluYXJ5LCBzb3VyY2UnOwogICAg
ICAgICAgICByZXR1cm4gMQogICAgICAgIDs7CiAgICBlc2FjOwogICAgbG9jYWwgVFlQRTsKICAg
IFRZUEU9IiR7My19IjsKICAgIGxvY2FsIE1JUlJPUjsKICAgIE1JUlJPUj0iJChudm1fZ2V0X21p
cnJvciAiJHtGTEFWT1J9IiAiJHtUWVBFfSIpIjsKICAgIGlmIFsgLXogIiR7TUlSUk9SfSIgXTsg
dGhlbgogICAgICAgIHJldHVybiAyOwogICAgZmk7CiAgICBsb2NhbCBWRVJTSU9OOwogICAgVkVS
U0lPTj0iJHs0fSI7CiAgICBpZiBbIC16ICIke1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAgbnZt
X2VyciAnQSB2ZXJzaW9uIG51bWJlciBpcyByZXF1aXJlZC4nOwogICAgICAgIHJldHVybiAzOwog
ICAgZmk7CiAgICBpZiBbICIke0tJTkR9IiA9ICdiaW5hcnknIF0gJiYgISBudm1fYmluYXJ5X2F2
YWlsYWJsZSAiJHtWRVJTSU9OfSI7IHRoZW4KICAgICAgICBudm1fZXJyICJObyBwcmVjb21waWxl
ZCBiaW5hcnkgYXZhaWxhYmxlIGZvciAke1ZFUlNJT059LiI7CiAgICAgICAgcmV0dXJuOwogICAg
Zmk7CiAgICBsb2NhbCBTTFVHOwogICAgU0xVRz0iJChudm1fZ2V0X2Rvd25sb2FkX3NsdWcgIiR7
RkxBVk9SfSIgIiR7S0lORH0iICIke1ZFUlNJT059IikiOwogICAgbG9jYWwgQ09NUFJFU1NJT047
CiAgICBDT01QUkVTU0lPTj0iJChudm1fZ2V0X2FydGlmYWN0X2NvbXByZXNzaW9uICIke1ZFUlNJ
T059IikiOwogICAgbG9jYWwgQ0hFQ0tTVU07CiAgICBDSEVDS1NVTT0iJChudm1fZ2V0X2NoZWNr
c3VtICIke0ZMQVZPUn0iICIke1RZUEV9IiAiJHtWRVJTSU9OfSIgIiR7U0xVR30iICIke0NPTVBS
RVNTSU9OfSIpIjsKICAgIGxvY2FsIHRtcGRpcjsKICAgIGlmIFsgIiR7S0lORH0iID0gJ2JpbmFy
eScgXTsgdGhlbgogICAgICAgIHRtcGRpcj0iJChudm1fY2FjaGVfZGlyKS9iaW4vJHtTTFVHfSI7
CiAgICBlbHNlCiAgICAgICAgdG1wZGlyPSIkKG52bV9jYWNoZV9kaXIpL3NyYy8ke1NMVUd9IjsK
ICAgIGZpOwogICAgY29tbWFuZCBta2RpciAtcCAiJHt0bXBkaXJ9L2ZpbGVzIiB8fCAoIG52bV9l
cnIgImNyZWF0aW5nIGRpcmVjdG9yeSAke3RtcGRpcn0vZmlsZXMgZmFpbGVkIjsKICAgIHJldHVy
biAzICk7CiAgICBsb2NhbCBUQVJCQUxMOwogICAgVEFSQkFMTD0iJHt0bXBkaXJ9LyR7U0xVR30u
JHtDT01QUkVTU0lPTn0iOwogICAgbG9jYWwgVEFSQkFMTF9VUkw7CiAgICBpZiBudm1fdmVyc2lv
bl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7VkVSU0lPTn0iIDAuMS4xNDsgdGhlbgogICAg
ICAgIFRBUkJBTExfVVJMPSIke01JUlJPUn0vJHtWRVJTSU9OfS8ke1NMVUd9LiR7Q09NUFJFU1NJ
T059IjsKICAgIGVsc2UKICAgICAgICBUQVJCQUxMX1VSTD0iJHtNSVJST1J9LyR7U0xVR30uJHtD
T01QUkVTU0lPTn0iOwogICAgZmk7CiAgICBpZiBbIC1yICIke1RBUkJBTEx9IiBdOyB0aGVuCiAg
ICAgICAgbnZtX2VyciAiTG9jYWwgY2FjaGUgZm91bmQ6ICQobnZtX3Nhbml0aXplX3BhdGggIiR7
VEFSQkFMTH0iKSI7CiAgICAgICAgaWYgbnZtX2NvbXBhcmVfY2hlY2tzdW0gIiR7VEFSQkFMTH0i
ICIke0NIRUNLU1VNfSIgPiAvZGV2L251bGwgMj4mMTsgdGhlbgogICAgICAgICAgICBudm1fZXJy
ICJDaGVja3N1bXMgbWF0Y2ghIFVzaW5nIGV4aXN0aW5nIGRvd25sb2FkZWQgYXJjaGl2ZSAkKG52
bV9zYW5pdGl6ZV9wYXRoICIke1RBUkJBTEx9IikiOwogICAgICAgICAgICBudm1fZWNobyAiJHtU
QVJCQUxMfSI7CiAgICAgICAgICAgIHJldHVybiAwOwogICAgICAgIGZpOwogICAgICAgIG52bV9j
b21wYXJlX2NoZWNrc3VtICIke1RBUkJBTEx9IiAiJHtDSEVDS1NVTX0iOwogICAgICAgIG52bV9l
cnIgIkNoZWNrc3VtIGNoZWNrIGZhaWxlZCEiOwogICAgICAgIG52bV9lcnIgIlJlbW92aW5nIHRo
ZSBicm9rZW4gbG9jYWwgY2FjaGUuLi4iOwogICAgICAgIGNvbW1hbmQgcm0gLXJmICIke1RBUkJB
TEx9IjsKICAgIGZpOwogICAgbnZtX2VyciAiRG93bmxvYWRpbmcgJHtUQVJCQUxMX1VSTH0uLi4i
OwogICAgbnZtX2Rvd25sb2FkIC1MIC1DIC0gIiR7UFJPR1JFU1NfQkFSfSIgIiR7VEFSQkFMTF9V
Ukx9IiAtbyAiJHtUQVJCQUxMfSIgfHwgKCBjb21tYW5kIHJtIC1yZiAiJHtUQVJCQUxMfSIgIiR7
dG1wZGlyfSI7CiAgICBudm1fZXJyICJkb3dubG9hZCBmcm9tICR7VEFSQkFMTF9VUkx9IGZhaWxl
ZCI7CiAgICByZXR1cm4gNCApOwogICAgaWYgbnZtX2dyZXAgJzQwNCBOb3QgRm91bmQnICIke1RB
UkJBTEx9IiA+IC9kZXYvbnVsbDsgdGhlbgogICAgICAgIGNvbW1hbmQgcm0gLXJmICIke1RBUkJB
TEx9IiAiJHt0bXBkaXJ9IjsKICAgICAgICBudm1fZXJyICJIVFRQIDQwNCBhdCBVUkwgJHtUQVJC
QUxMX1VSTH0iOwogICAgICAgIHJldHVybiA1OwogICAgZmk7CiAgICBudm1fY29tcGFyZV9jaGVj
a3N1bSAiJHtUQVJCQUxMfSIgIiR7Q0hFQ0tTVU19IiB8fCAoIGNvbW1hbmQgcm0gLXJmICIke3Rt
cGRpcn0vZmlsZXMiOwogICAgcmV0dXJuIDYgKTsKICAgIG52bV9lY2hvICIke1RBUkJBTEx9Igp9
Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2VjaG8gKCkgCnsgCiAgICBjb21tYW5kIHByaW50ZiAlc1xcbiAiJCoiIDI+IC9kZXYvbnVs
bAp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2VjaG9fd2l0aF9jb2xvcnMgKCkgCnsgCiAgICBjb21tYW5kIHByaW50ZiAlYlxcbiAiJCoi
IDI+IC9kZXYvbnVsbAp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Vuc3VyZV9kZWZhdWx0X3NldCAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT047CiAgICBWRVJT
SU9OPSIkMSI7CiAgICBpZiBbIC16ICIke1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAgbnZtX2Vy
ciAnbnZtX2Vuc3VyZV9kZWZhdWx0X3NldDogYSB2ZXJzaW9uIGlzIHJlcXVpcmVkJzsKICAgICAg
ICByZXR1cm4gMTsKICAgIGVsc2UKICAgICAgICBpZiBudm1fYWxpYXMgZGVmYXVsdCA+IC9kZXYv
bnVsbCAyPiYxOyB0aGVuCiAgICAgICAgICAgIHJldHVybiAwOwogICAgICAgIGZpOwogICAgZmk7
CiAgICBsb2NhbCBPVVRQVVQ7CiAgICBPVVRQVVQ9IiQobnZtIGFsaWFzIGRlZmF1bHQgIiR7VkVS
U0lPTn0iKSI7CiAgICBsb2NhbCBFWElUX0NPREU7CiAgICBFWElUX0NPREU9IiQ/IjsKICAgIG52
bV9lY2hvICJDcmVhdGluZyBkZWZhdWx0IGFsaWFzOiAke09VVFBVVH0iOwogICAgcmV0dXJuICRF
WElUX0NPREUKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Vuc3VyZV92ZXJzaW9uX2luc3RhbGxlZCAoKSAKeyAKICAgIGxvY2FsIFBST1ZJREVEX1ZF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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Vuc3VyZV92ZXJzaW9uX3ByZWZpeCAoKSAKeyAKICAgIGxvY2FsIE5WTV9WRVJTSU9OOwog
ICAgTlZNX1ZFUlNJT049IiQobnZtX3N0cmlwX2lvanNfcHJlZml4ICIkezEtfSIgfCBjb21tYW5k
IHNlZCAtZSAncy9eXChbMC05XVwpL3ZcMS9nJykiOwogICAgaWYgbnZtX2lzX2lvanNfdmVyc2lv
biAiJHsxLX0iOyB0aGVuCiAgICAgICAgbnZtX2FkZF9pb2pzX3ByZWZpeCAiJHtOVk1fVkVSU0lP
Tn0iOwogICAgZWxzZQogICAgICAgIG52bV9lY2hvICIke05WTV9WRVJTSU9OfSI7CiAgICBmaQp9
Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2VyciAoKSAKeyAKICAgIG52bV9lY2hvICIkQCIgMT4mMgp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Vycl93aXRoX2NvbG9ycyAoKSAKeyAKICAgIG52bV9lY2hvX3dpdGhfY29sb3JzICIkQCIg
MT4mMgp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2V4dHJhY3RfdGFyYmFsbCAoKSAKeyAKICAgIGlmIFsgIiQjIiAtbmUgNCBdOyB0aGVuCiAg
ICAgICAgbnZtX2VyciAnbnZtX2V4dHJhY3RfdGFyYmFsbCByZXF1aXJlcyBleGFjdGx5IDQgYXJn
dW1lbnRzJzsKICAgICAgICByZXR1cm4gNTsKICAgIGZpOwogICAgbG9jYWwgTlZNX09TOwogICAg
TlZNX09TPSIkezEtfSI7CiAgICBsb2NhbCBWRVJTSU9OOwogICAgVkVSU0lPTj0iJHsyLX0iOwog
ICAgbG9jYWwgVEFSQkFMTDsKICAgIFRBUkJBTEw9IiR7My19IjsKICAgIGxvY2FsIFRNUERJUjsK
ICAgIFRNUERJUj0iJHs0LX0iOwogICAgbG9jYWwgdGFyX2NvbXByZXNzaW9uX2ZsYWc7CiAgICB0
YXJfY29tcHJlc3Npb25fZmxhZz0neic7CiAgICBpZiBudm1fc3VwcG9ydHNfeHogIiR7VkVSU0lP
Tn0iOyB0aGVuCiAgICAgICAgdGFyX2NvbXByZXNzaW9uX2ZsYWc9J0onOwogICAgZmk7CiAgICBs
b2NhbCB0YXI7CiAgICB0YXI9J3Rhcic7CiAgICBpZiBbICIke05WTV9PU30iID0gJ2FpeCcgXTsg
dGhlbgogICAgICAgIHRhcj0nZ3Rhcic7CiAgICBmaTsKICAgIGlmIFsgIiR7TlZNX09TfSIgPSAn
b3BlbmJzZCcgXTsgdGhlbgogICAgICAgIGlmIFsgIiR7dGFyX2NvbXByZXNzaW9uX2ZsYWd9IiA9
ICdKJyBdOyB0aGVuCiAgICAgICAgICAgIGNvbW1hbmQgeHpjYXQgIiR7VEFSQkFMTH0iIHwgIiR7
dGFyfSIgLXhmIC0gLUMgIiR7VE1QRElSfSIgLXMgJy9bXlwvXSpcLy8vJyB8fCByZXR1cm4gMTsK
ICAgICAgICBlbHNlCiAgICAgICAgICAgIGNvbW1hbmQgIiR7dGFyfSIgLXgke3Rhcl9jb21wcmVz
c2lvbl9mbGFnfWYgIiR7VEFSQkFMTH0iIC1DICIke1RNUERJUn0iIC1zICcvW15cL10qXC8vLycg
fHwgcmV0dXJuIDE7CiAgICAgICAgZmk7CiAgICBlbHNlCiAgICAgICAgY29tbWFuZCAiJHt0YXJ9
IiAteCR7dGFyX2NvbXByZXNzaW9uX2ZsYWd9ZiAiJHtUQVJCQUxMfSIgLUMgIiR7VE1QRElSfSIg
LS1zdHJpcC1jb21wb25lbnRzIDEgfHwgcmV0dXJuIDE7CiAgICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2ZpbmRfbnZtcmMgKCkgCnsgCiAgICBsb2NhbCBkaXI7CiAgICBkaXI9IiQobnZtX2ZpbmRf
dXAgJy5udm1yYycpIjsKICAgIGlmIFsgLWUgIiR7ZGlyfS8ubnZtcmMiIF07IHRoZW4KICAgICAg
ICBudm1fZWNobyAiJHtkaXJ9Ly5udm1yYyI7CiAgICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2ZpbmRfcHJvamVjdF9kaXIgKCkgCnsgCiAgICBsb2NhbCBwYXRoXzsKICAgIHBhdGhfPSIk
e1BXRH0iOwogICAgd2hpbGUgWyAiJHtwYXRoX30iICE9ICIiIF0gJiYgWyAiJHtwYXRoX30iICE9
ICcuJyBdICYmIFsgISAtZiAiJHtwYXRoX30vcGFja2FnZS5qc29uIiBdICYmIFsgISAtZCAiJHtw
YXRoX30vbm9kZV9tb2R1bGVzIiBdOyBkbwogICAgICAgIHBhdGhfPSR7cGF0aF8lLyp9OwogICAg
ZG9uZTsKICAgIG52bV9lY2hvICIke3BhdGhffSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2ZpbmRfdXAgKCkgCnsgCiAgICBsb2NhbCBwYXRoXzsKICAgIHBhdGhfPSIke1BXRH0iOwog
ICAgd2hpbGUgWyAiJHtwYXRoX30iICE9ICIiIF0gJiYgWyAiJHtwYXRoX30iICE9ICcuJyBdICYm
IFsgISAtZiAiJHtwYXRoX30vJHsxLX0iIF07IGRvCiAgICAgICAgcGF0aF89JHtwYXRoXyUvKn07
CiAgICBkb25lOwogICAgbnZtX2VjaG8gIiR7cGF0aF99Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2Zvcm1hdF92ZXJzaW9uICgpIAp7IAogICAgbG9jYWwgVkVSU0lPTjsKICAgIFZFUlNJT049
IiQobnZtX2Vuc3VyZV92ZXJzaW9uX3ByZWZpeCAiJHsxLX0iKSI7CiAgICBsb2NhbCBOVU1fR1JP
VVBTOwogICAgTlVNX0dST1VQUz0iJChudm1fbnVtX3ZlcnNpb25fZ3JvdXBzICIke1ZFUlNJT059
IikiOwogICAgaWYgWyAiJHtOVU1fR1JPVVBTfSIgLWx0IDMgXTsgdGhlbgogICAgICAgIG52bV9m
b3JtYXRfdmVyc2lvbiAiJHtWRVJTSU9OJS59LjAiOwogICAgZWxzZQogICAgICAgIG52bV9lY2hv
ICIke1ZFUlNJT059IiB8IGNvbW1hbmQgY3V0IC1mMS0zIC1kLjsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9hcmNoICgpIAp7IAogICAgbG9jYWwgSE9TVF9BUkNIOwogICAgbG9jYWwgTlZNX09T
OwogICAgbG9jYWwgRVhJVF9DT0RFOwogICAgbG9jYWwgTE9OR19CSVQ7CiAgICBOVk1fT1M9IiQo
bnZtX2dldF9vcykiOwogICAgaWYgWyAiXyR7TlZNX09TfSIgPSAiX3N1bm9zIiBdOyB0aGVuCiAg
ICAgICAgaWYgSE9TVF9BUkNIPSQocGtnX2luZm8gLVEgTUFDSElORV9BUkNIIHBrZ19pbnN0YWxs
KTsgdGhlbgogICAgICAgICAgICBIT1NUX0FSQ0g9JChudm1fZWNobyAiJHtIT1NUX0FSQ0h9IiB8
IGNvbW1hbmQgdGFpbCAtMSk7CiAgICAgICAgZWxzZQogICAgICAgICAgICBIT1NUX0FSQ0g9JChp
c2FpbmZvIC1uKTsKICAgICAgICBmaTsKICAgIGVsc2UKICAgICAgICBpZiBbICJfJHtOVk1fT1N9
IiA9ICJfYWl4IiBdOyB0aGVuCiAgICAgICAgICAgIEhPU1RfQVJDSD1wcGM2NDsKICAgICAgICBl
bHNlCiAgICAgICAgICAgIEhPU1RfQVJDSD0iJChjb21tYW5kIHVuYW1lIC1tKSI7CiAgICAgICAg
ICAgIExPTkdfQklUPSIkKGdldGNvbmYgTE9OR19CSVQgMj4gL2Rldi9udWxsKSI7CiAgICAgICAg
Zmk7CiAgICBmaTsKICAgIGxvY2FsIE5WTV9BUkNIOwogICAgY2FzZSAiJHtIT1NUX0FSQ0h9IiBp
biAKICAgICAgICB4ODZfNjQgfCBhbWQ2NCkKICAgICAgICAgICAgTlZNX0FSQ0g9Ing2NCIKICAg
ICAgICA7OwogICAgICAgIGkqODYpCiAgICAgICAgICAgIE5WTV9BUkNIPSJ4ODYiCiAgICAgICAg
OzsKICAgICAgICBhYXJjaDY0IHwgYXJtdjhsKQogICAgICAgICAgICBOVk1fQVJDSD0iYXJtNjQi
CiAgICAgICAgOzsKICAgICAgICAqKQogICAgICAgICAgICBOVk1fQVJDSD0iJHtIT1NUX0FSQ0h9
IgogICAgICAgIDs7CiAgICBlc2FjOwogICAgaWYgWyAiXyR7TE9OR19CSVR9IiA9ICJfMzIiIF0g
JiYgWyAiJHtOVk1fQVJDSH0iID0gIng2NCIgXTsgdGhlbgogICAgICAgIE5WTV9BUkNIPSJ4ODYi
OwogICAgZmk7CiAgICBpZiBbICIkKHVuYW1lKSIgPSAiTGludXgiIF0gJiYgWyAiJHtOVk1fQVJD
SH0iID0gYXJtNjQgXSAmJiBbICIkKGNvbW1hbmQgb2QgLUFuIC10IHgxIC1qIDQgLU4gMSAiL3Ni
aW4vaW5pdCIgMj4gL2Rldi9udWxsKSIgPSAnIDAxJyBdOyB0aGVuCiAgICAgICAgTlZNX0FSQ0g9
YXJtdjdsOwogICAgICAgIEhPU1RfQVJDSD1hcm12N2w7CiAgICBmaTsKICAgIGlmIFsgLWYgIi9l
dGMvYWxwaW5lLXJlbGVhc2UiIF07IHRoZW4KICAgICAgICBOVk1fQVJDSD14NjQtbXVzbDsKICAg
IGZpOwogICAgbnZtX2VjaG8gIiR7TlZNX0FSQ0h9Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9hcnRpZmFjdF9jb21wcmVzc2lvbiAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT047CiAg
ICBWRVJTSU9OPSIkezEtfSI7CiAgICBsb2NhbCBOVk1fT1M7CiAgICBOVk1fT1M9IiQobnZtX2dl
dF9vcykiOwogICAgbG9jYWwgQ09NUFJFU1NJT047CiAgICBDT01QUkVTU0lPTj0ndGFyLmd6JzsK
ICAgIGlmIFsgIl8ke05WTV9PU30iID0gJ193aW4nIF07IHRoZW4KICAgICAgICBDT01QUkVTU0lP
Tj0nemlwJzsKICAgIGVsc2UKICAgICAgICBpZiBudm1fc3VwcG9ydHNfeHogIiR7VkVSU0lPTn0i
OyB0aGVuCiAgICAgICAgICAgIENPTVBSRVNTSU9OPSd0YXIueHonOwogICAgICAgIGZpOwogICAg
Zmk7CiAgICBudm1fZWNobyAiJHtDT01QUkVTU0lPTn0iCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9jaGVja3N1bSAoKSAKeyAKICAgIGxvY2FsIEZMQVZPUjsKICAgIGNhc2UgIiR7MS19
IiBpbiAKICAgICAgICBub2RlIHwgaW9qcykKICAgICAgICAgICAgRkxBVk9SPSIkezF9IgogICAg
ICAgIDs7CiAgICAgICAgKikKICAgICAgICAgICAgbnZtX2VyciAnc3VwcG9ydGVkIGZsYXZvcnM6
IG5vZGUsIGlvanMnOwogICAgICAgICAgICByZXR1cm4gMgogICAgICAgIDs7CiAgICBlc2FjOwog
ICAgbG9jYWwgTUlSUk9SOwogICAgTUlSUk9SPSIkKG52bV9nZXRfbWlycm9yICIke0ZMQVZPUn0i
ICIkezItfSIpIjsKICAgIGlmIFsgLXogIiR7TUlSUk9SfSIgXTsgdGhlbgogICAgICAgIHJldHVy
biAxOwogICAgZmk7CiAgICBsb2NhbCBTSEFTVU1TX1VSTDsKICAgIGlmIFsgIiQobnZtX2dldF9j
aGVja3N1bV9hbGcpIiA9ICdzaGEtMjU2JyBdOyB0aGVuCiAgICAgICAgU0hBU1VNU19VUkw9IiR7
TUlSUk9SfS8kezN9L1NIQVNVTVMyNTYudHh0IjsKICAgIGVsc2UKICAgICAgICBTSEFTVU1TX1VS
TD0iJHtNSVJST1J9LyR7M30vU0hBU1VNUy50eHQiOwogICAgZmk7CiAgICBudm1fZG93bmxvYWQg
LUwgLXMgIiR7U0hBU1VNU19VUkx9IiAtbyAtIHwgY29tbWFuZCBhd2sgInsgaWYgKFwiJHs0fS4k
ezV9XCIgPT0gXCQyKSBwcmludCBcJDF9Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9jaGVja3N1bV9hbGcgKCkgCnsgCiAgICBsb2NhbCBOVk1fQ0hFQ0tTVU1fQklOOwog
ICAgTlZNX0NIRUNLU1VNX0JJTj0iJChudm1fZ2V0X2NoZWNrc3VtX2JpbmFyeSAyPiAvZGV2L251
bGwpIjsKICAgIGNhc2UgIiR7TlZNX0NIRUNLU1VNX0JJTi19IiBpbiAKICAgICAgICBzaGEyNTZz
dW0gfCBzaGFzdW0gfCBzaGEyNTYgfCBnc2hhMjU2c3VtIHwgb3BlbnNzbCB8IGJzc2wpCiAgICAg
ICAgICAgIG52bV9lY2hvICdzaGEtMjU2JwogICAgICAgIDs7CiAgICAgICAgc2hhMXN1bSB8IHNo
YTEpCiAgICAgICAgICAgIG52bV9lY2hvICdzaGEtMScKICAgICAgICA7OwogICAgICAgICopCiAg
ICAgICAgICAgIG52bV9nZXRfY2hlY2tzdW1fYmluYXJ5OwogICAgICAgICAgICByZXR1cm4gJD8K
ICAgICAgICA7OwogICAgZXNhYwp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9jaGVja3N1bV9iaW5hcnkgKCkgCnsgCiAgICBpZiBudm1faGFzX25vbl9hbGlhc2Vk
ICdzaGEyNTZzdW0nOyB0aGVuCiAgICAgICAgbnZtX2VjaG8gJ3NoYTI1NnN1bSc7CiAgICBlbHNl
CiAgICAgICAgaWYgbnZtX2hhc19ub25fYWxpYXNlZCAnc2hhc3VtJzsgdGhlbgogICAgICAgICAg
ICBudm1fZWNobyAnc2hhc3VtJzsKICAgICAgICBlbHNlCiAgICAgICAgICAgIGlmIG52bV9oYXNf
bm9uX2FsaWFzZWQgJ3NoYTI1Nic7IHRoZW4KICAgICAgICAgICAgICAgIG52bV9lY2hvICdzaGEy
NTYnOwogICAgICAgICAgICBlbHNlCiAgICAgICAgICAgICAgICBpZiBudm1faGFzX25vbl9hbGlh
c2VkICdnc2hhMjU2c3VtJzsgdGhlbgogICAgICAgICAgICAgICAgICAgIG52bV9lY2hvICdnc2hh
MjU2c3VtJzsKICAgICAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAgICAgICAgICBpZiBudm1f
aGFzX25vbl9hbGlhc2VkICdvcGVuc3NsJzsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICBu
dm1fZWNobyAnb3BlbnNzbCc7CiAgICAgICAgICAgICAgICAgICAgZWxzZQogICAgICAgICAgICAg
ICAgICAgICAgICBpZiBudm1faGFzX25vbl9hbGlhc2VkICdic3NsJzsgdGhlbgogICAgICAgICAg
ICAgICAgICAgICAgICAgICAgbnZtX2VjaG8gJ2Jzc2wnOwogICAgICAgICAgICAgICAgICAgICAg
ICBlbHNlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiBudm1faGFzX25vbl9hbGlhc2Vk
ICdzaGExc3VtJzsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG52bV9lY2hv
ICdzaGExc3VtJzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICBpZiBudm1faGFzX25vbl9hbGlhc2VkICdzaGExJzsgdGhlbgog
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudm1fZWNobyAnc2hhMSc7CiAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZQogICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICBudm1fZXJyICdVbmFsaWFzZWQgc2hhMjU2c3VtLCBzaGFzdW0sIHNoYTI1Niwg
Z3NoYTI1NnN1bSwgb3BlbnNzbCwgb3IgYnNzbCBub3QgZm91bmQuJzsKICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgbnZtX2VyciAnVW5hbGlhc2VkIHNoYTFzdW0gb3Igc2hhMSBu
b3QgZm91bmQuJzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDE7
CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmk7CiAgICAgICAgICAgICAgICAgICAg
ICAgICAgICBmaTsKICAgICAgICAgICAgICAgICAgICAgICAgZmk7CiAgICAgICAgICAgICAgICAg
ICAgZmk7CiAgICAgICAgICAgICAgICBmaTsKICAgICAgICAgICAgZmk7CiAgICAgICAgZmk7CiAg
ICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9jb2xvcnMgKCkgCnsgCiAgICBsb2NhbCBDT0xPUjsKICAgIGxvY2FsIFNZU19DT0xP
UjsKICAgIGxvY2FsIENPTE9SUzsKICAgIENPTE9SUz0iJHtOVk1fQ09MT1JTOi1ieWdyZX0iOwog
ICAgY2FzZSAkMSBpbiAKICAgICAgICAxKQogICAgICAgICAgICBDT0xPUj0kKG52bV9wcmludF9j
b2xvcl9jb2RlICIkKGVjaG8gIiRDT0xPUlMiIHwgYXdrICd7IHByaW50IHN1YnN0cigkMCwgMSwg
MSk7IH0nKSIpCiAgICAgICAgOzsKICAgICAgICAyKQogICAgICAgICAgICBDT0xPUj0kKG52bV9w
cmludF9jb2xvcl9jb2RlICIkKGVjaG8gIiRDT0xPUlMiIHwgYXdrICd7IHByaW50IHN1YnN0cigk
MCwgMiwgMSk7IH0nKSIpCiAgICAgICAgOzsKICAgICAgICAzKQogICAgICAgICAgICBDT0xPUj0k
KG52bV9wcmludF9jb2xvcl9jb2RlICIkKGVjaG8gIiRDT0xPUlMiIHwgYXdrICd7IHByaW50IHN1
YnN0cigkMCwgMywgMSk7IH0nKSIpCiAgICAgICAgOzsKICAgICAgICA0KQogICAgICAgICAgICBD
T0xPUj0kKG52bV9wcmludF9jb2xvcl9jb2RlICIkKGVjaG8gIiRDT0xPUlMiIHwgYXdrICd7IHBy
aW50IHN1YnN0cigkMCwgNCwgMSk7IH0nKSIpCiAgICAgICAgOzsKICAgICAgICA1KQogICAgICAg
ICAgICBDT0xPUj0kKG52bV9wcmludF9jb2xvcl9jb2RlICIkKGVjaG8gIiRDT0xPUlMiIHwgYXdr
ICd7IHByaW50IHN1YnN0cigkMCwgNSwgMSk7IH0nKSIpCiAgICAgICAgOzsKICAgICAgICA2KQog
ICAgICAgICAgICBTWVNfQ09MT1I9JChudm1fcHJpbnRfY29sb3JfY29kZSAiJChlY2hvICIkQ09M
T1JTIiB8IGF3ayAneyBwcmludCBzdWJzdHIoJDAsIDIsIDEpOyB9JykiKTsKICAgICAgICAgICAg
Q09MT1I9JChudm1fZWNobyAiJFNZU19DT0xPUiIgfCBjb21tYW5kIHRyICcwOycgJzE7JykKICAg
ICAgICA7OwogICAgICAgICopCiAgICAgICAgICAgIG52bV9lcnIgIkludmFsaWQgY29sb3IgaW5k
ZXgsICR7MS19IjsKICAgICAgICAgICAgcmV0dXJuIDEKICAgICAgICA7OwogICAgZXNhYzsKICAg
IG52bV9lY2hvICIkQ09MT1IiCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9kZWZhdWx0X3BhY2thZ2VzICgpIAp7IAogICAgbG9jYWwgTlZNX0RFRkFVTFRfUEFD
S0FHRV9GSUxFOwogICAgTlZNX0RFRkFVTFRfUEFDS0FHRV9GSUxFPSIke05WTV9ESVJ9L2RlZmF1
bHQtcGFja2FnZXMiOwogICAgaWYgWyAtZiAiJHtOVk1fREVGQVVMVF9QQUNLQUdFX0ZJTEV9IiBd
OyB0aGVuCiAgICAgICAgY29tbWFuZCBhd2sgLXYgZmlsZW5hbWU9IiR7TlZNX0RFRkFVTFRfUEFD
S0FHRV9GSUxFfSIgJwogICAgICAvXltbOnNwYWNlOl1dKiMvIHsgbmV4dCB9ICAgICAgICAgICAg
ICAgICAgICAgIyBTa2lwIGxpbmVzIHRoYXQgYmVnaW4gd2l0aCAjCiAgICAgIC9eW1s6c3BhY2U6
XV0qJC8geyBuZXh0IH0gICAgICAgICAgICAgICAgICAgICAjIFNraXAgZW1wdHkgbGluZXMKICAg
ICAgL1tbOnNwYWNlOl1dLyAmJiAhL15bWzpzcGFjZTpdXSojLyB7CiAgICAgICAgcHJpbnQgIk9u
bHkgb25lIHBhY2thZ2UgcGVyIGxpbmUgaXMgYWxsb3dlZCBpbiBgIiBmaWxlbmFtZSAiYC4gUGxl
YXNlIHJlbW92ZSBhbnkgbGluZXMgd2l0aCBtdWx0aXBsZSBzcGFjZS1zZXBhcmF0ZWQgdmFsdWVz
LiIgPiAiL2Rldi9zdGRlcnIiCiAgICAgICAgZXJyID0gMQogICAgICAgIGV4aXQgMQogICAgICB9
CiAgICAgIHsKICAgICAgICBpZiAoTlIgPiAxICYmICFwcmV2X3NwYWNlKSBwcmludGYgIiAiCiAg
ICAgICAgcHJpbnRmICIlcyIsICQwCiAgICAgICAgcHJldl9zcGFjZSA9IDAKICAgICAgfQogICAg
JyAiJHtOVk1fREVGQVVMVF9QQUNLQUdFX0ZJTEV9IjsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9kb3dubG9hZF9zbHVnICgpIAp7IAogICAgbG9jYWwgRkxBVk9SOwogICAgY2FzZSAi
JHsxLX0iIGluIAogICAgICAgIG5vZGUgfCBpb2pzKQogICAgICAgICAgICBGTEFWT1I9IiR7MX0i
CiAgICAgICAgOzsKICAgICAgICAqKQogICAgICAgICAgICBudm1fZXJyICdzdXBwb3J0ZWQgZmxh
dm9yczogbm9kZSwgaW9qcyc7CiAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgOzsKICAgIGVz
YWM7CiAgICBsb2NhbCBLSU5EOwogICAgY2FzZSAiJHsyLX0iIGluIAogICAgICAgIGJpbmFyeSB8
IHNvdXJjZSkKICAgICAgICAgICAgS0lORD0iJHsyfSIKICAgICAgICA7OwogICAgICAgICopCiAg
ICAgICAgICAgIG52bV9lcnIgJ3N1cHBvcnRlZCBraW5kczogYmluYXJ5LCBzb3VyY2UnOwogICAg
ICAgICAgICByZXR1cm4gMgogICAgICAgIDs7CiAgICBlc2FjOwogICAgbG9jYWwgVkVSU0lPTjsK
ICAgIFZFUlNJT049IiR7My19IjsKICAgIGxvY2FsIE5WTV9PUzsKICAgIE5WTV9PUz0iJChudm1f
Z2V0X29zKSI7CiAgICBsb2NhbCBOVk1fQVJDSDsKICAgIE5WTV9BUkNIPSIkKG52bV9nZXRfYXJj
aCkiOwogICAgaWYgISBudm1faXNfbWVyZ2VkX25vZGVfdmVyc2lvbiAiJHtWRVJTSU9OfSI7IHRo
ZW4KICAgICAgICBpZiBbICIke05WTV9BUkNIfSIgPSAnYXJtdjZsJyBdIHx8IFsgIiR7TlZNX0FS
Q0h9IiA9ICdhcm12N2wnIF07IHRoZW4KICAgICAgICAgICAgTlZNX0FSQ0g9ImFybS1waSI7CiAg
ICAgICAgZmk7CiAgICBmaTsKICAgIGlmIG52bV92ZXJzaW9uX2dyZWF0ZXIgJzE0LjE3LjAnICIk
e1ZFUlNJT059IiB8fCAoIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtW
RVJTSU9OfSIgJzE1LjAuMCcgJiYgbnZtX3ZlcnNpb25fZ3JlYXRlciAnMTYuMC4wJyAiJHtWRVJT
SU9OfSIgKTsgdGhlbgogICAgICAgIGlmIFsgIl8ke05WTV9PU30iID0gJ19kYXJ3aW4nIF0gJiYg
WyAiJHtOVk1fQVJDSH0iID0gJ2FybTY0JyBdOyB0aGVuCiAgICAgICAgICAgIE5WTV9BUkNIPXg2
NDsKICAgICAgICBmaTsKICAgIGZpOwogICAgaWYgWyAiJHtLSU5EfSIgPSAnYmluYXJ5JyBdOyB0
aGVuCiAgICAgICAgbnZtX2VjaG8gIiR7RkxBVk9SfS0ke1ZFUlNJT059LSR7TlZNX09TfS0ke05W
TV9BUkNIfSI7CiAgICBlbHNlCiAgICAgICAgaWYgWyAiJHtLSU5EfSIgPSAnc291cmNlJyBdOyB0
aGVuCiAgICAgICAgICAgIG52bV9lY2hvICIke0ZMQVZPUn0tJHtWRVJTSU9OfSI7CiAgICAgICAg
Zmk7CiAgICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9sYXRlc3QgKCkgCnsgCiAgICBsb2NhbCBOVk1fTEFURVNUX1VSTDsKICAgIGxvY2Fs
IENVUkxfQ09NUFJFU1NFRF9GTEFHOwogICAgaWYgbnZtX2hhcyAiY3VybCI7IHRoZW4KICAgICAg
ICBpZiBudm1fY3VybF91c2VfY29tcHJlc3Npb247IHRoZW4KICAgICAgICAgICAgQ1VSTF9DT01Q
UkVTU0VEX0ZMQUc9Ii0tY29tcHJlc3NlZCI7CiAgICAgICAgZmk7CiAgICAgICAgTlZNX0xBVEVT
VF9VUkw9IiQoY3VybCAke0NVUkxfQ09NUFJFU1NFRF9GTEFHOi19IC1xIC13ICIle3VybF9lZmZl
Y3RpdmV9XFxuIiAtTCAtcyAtUyBodHRwczovL2xhdGVzdC5udm0uc2ggLW8gL2Rldi9udWxsKSI7
CiAgICBlbHNlCiAgICAgICAgaWYgbnZtX2hhcyAid2dldCI7IHRoZW4KICAgICAgICAgICAgTlZN
X0xBVEVTVF9VUkw9IiQod2dldCAtcSBodHRwczovL2xhdGVzdC5udm0uc2ggLS1zZXJ2ZXItcmVz
cG9uc2UgLU8gL2Rldi9udWxsIDI+JjEgfCBjb21tYW5kIGF3ayAnL14gIExvY2F0aW9uOiAve0RF
U1Q9JDJ9IEVORHsgcHJpbnQgREVTVCB9JykiOwogICAgICAgIGVsc2UKICAgICAgICAgICAgbnZt
X2VyciAnbnZtIG5lZWRzIGN1cmwgb3Igd2dldCB0byBwcm9jZWVkLic7CiAgICAgICAgICAgIHJl
dHVybiAxOwogICAgICAgIGZpOwogICAgZmk7CiAgICBpZiBbIC16ICIke05WTV9MQVRFU1RfVVJM
fSIgXTsgdGhlbgogICAgICAgIG52bV9lcnIgImh0dHBzOi8vbGF0ZXN0Lm52bS5zaCBkaWQgbm90
IHJlZGlyZWN0IHRvIHRoZSBsYXRlc3QgcmVsZWFzZSBvbiBHaXRIdWIiOwogICAgICAgIHJldHVy
biAyOwogICAgZmk7CiAgICBudm1fZWNobyAiJHtOVk1fTEFURVNUX1VSTCMjKi99Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9tYWtlX2pvYnMgKCkgCnsgCiAgICBpZiBudm1faXNfbmF0dXJhbF9udW0gIiR7MS19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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9taW5vcl92ZXJzaW9uICgpIAp7IAogICAgbG9jYWwgVkVSU0lPTjsKICAgIFZFUlNJ
T049IiQxIjsKICAgIGlmIFsgLXogIiR7VkVSU0lPTn0iIF07IHRoZW4KICAgICAgICBudm1fZXJy
ICdhIHZlcnNpb24gaXMgcmVxdWlyZWQnOwogICAgICAgIHJldHVybiAxOwogICAgZmk7CiAgICBj
YXNlICIke1ZFUlNJT059IiBpbiAKICAgICAgICB2IHwgLiogfCAqLi4qIHwgdipbIS4wMTIzNDU2
Nzg5XSogfCBbIXZdKlshLjAxMjM0NTY3ODldKiB8IFshdjAxMjM0NTY3ODldKiB8IHZbITAxMjM0
NTY3ODldKikKICAgICAgICAgICAgbnZtX2VyciAnaW52YWxpZCB2ZXJzaW9uIG51bWJlcic7CiAg
ICAgICAgICAgIHJldHVybiAyCiAgICAgICAgOzsKICAgIGVzYWM7CiAgICBsb2NhbCBQUkVGSVhF
RF9WRVJTSU9OOwogICAgUFJFRklYRURfVkVSU0lPTj0iJChudm1fZm9ybWF0X3ZlcnNpb24gIiR7
VkVSU0lPTn0iKSI7CiAgICBsb2NhbCBNSU5PUjsKICAgIE1JTk9SPSIkKG52bV9lY2hvICIke1BS
RUZJWEVEX1ZFUlNJT059IiB8IG52bV9ncmVwIC1lICdedicgfCBjb21tYW5kIGN1dCAtYzItIHwg
Y29tbWFuZCBjdXQgLWQgLiAtZiAxLDIpIjsKICAgIGlmIFsgLXogIiR7TUlOT1J9IiBdOyB0aGVu
CiAgICAgICAgbnZtX2VyciAnaW52YWxpZCB2ZXJzaW9uIG51bWJlciEgKHBsZWFzZSByZXBvcnQg
dGhpcyknOwogICAgICAgIHJldHVybiAzOwogICAgZmk7CiAgICBudm1fZWNobyAiJHtNSU5PUn0i
Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9taXJyb3IgKCkgCnsgCiAgICBsb2NhbCBOVk1fTUlSUk9SOwogICAgTlZNX01JUlJP
Uj0nJzsKICAgIGNhc2UgIiR7MX0tJHsyfSIgaW4gCiAgICAgICAgbm9kZS1zdGQpCiAgICAgICAg
ICAgIE5WTV9NSVJST1I9IiR7TlZNX05PREVKU19PUkdfTUlSUk9SOi1odHRwczovL25vZGVqcy5v
cmcvZGlzdH0iCiAgICAgICAgOzsKICAgICAgICBpb2pzLXN0ZCkKICAgICAgICAgICAgTlZNX01J
UlJPUj0iJHtOVk1fSU9KU19PUkdfTUlSUk9SOi1odHRwczovL2lvanMub3JnL2Rpc3R9IgogICAg
ICAgIDs7CiAgICAgICAgKikKICAgICAgICAgICAgbnZtX2VyciAndW5rbm93biB0eXBlIG9mIG5v
ZGUuanMgb3IgaW8uanMgcmVsZWFzZSc7CiAgICAgICAgICAgIHJldHVybiAxCiAgICAgICAgOzsK
ICAgIGVzYWM7CiAgICBjYXNlICIke05WTV9NSVJST1J9IiBpbiAKICAgICAgICAqXGAqIHwgKlxc
KiB8ICpcJyogfCAqXCgqIHwgKicgJyopCiAgICAgICAgICAgIG52bV9lcnIgJyROVk1fTk9ERUpT
X09SR19NSVJST1IgYW5kICROVk1fSU9KU19PUkdfTUlSUk9SIG1heSBvbmx5IGNvbnRhaW4gYSBV
UkwnOwogICAgICAgICAgICByZXR1cm4gMgogICAgICAgIDs7CiAgICBlc2FjOwogICAgaWYgISBu
dm1fZWNobyAiJHtOVk1fTUlSUk9SfSIgfCBjb21tYW5kIGF3ayAneyAkMCB+ICJeaHR0cHM/Oi8v
W2EtekEtWjAtOS4vXy1dKyQiIH0nOyB0aGVuCiAgICAgICAgbnZtX2VyciAnJE5WTV9OT0RFSlNf
T1JHX01JUlJPUiBhbmQgJE5WTV9JT0pTX09SR19NSVJST1IgbWF5IG9ubHkgY29udGFpbiBhIFVS
TCc7CiAgICAgICAgcmV0dXJuIDI7CiAgICBmaTsKICAgIG52bV9lY2hvICIke05WTV9NSVJST1J9
Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dldF9vcyAoKSAKeyAKICAgIGxvY2FsIE5WTV9VTkFNRTsKICAgIE5WTV9VTkFNRT0iJChj
b21tYW5kIHVuYW1lIC1hKSI7CiAgICBsb2NhbCBOVk1fT1M7CiAgICBjYXNlICIke05WTV9VTkFN
RX0iIGluIAogICAgICAgIExpbnV4XCAqKQogICAgICAgICAgICBOVk1fT1M9bGludXgKICAgICAg
ICA7OwogICAgICAgIERhcndpblwgKikKICAgICAgICAgICAgTlZNX09TPWRhcndpbgogICAgICAg
IDs7CiAgICAgICAgU3VuT1NcICopCiAgICAgICAgICAgIE5WTV9PUz1zdW5vcwogICAgICAgIDs7
CiAgICAgICAgRnJlZUJTRFwgKikKICAgICAgICAgICAgTlZNX09TPWZyZWVic2QKICAgICAgICA7
OwogICAgICAgIE9wZW5CU0RcICopCiAgICAgICAgICAgIE5WTV9PUz1vcGVuYnNkCiAgICAgICAg
OzsKICAgICAgICBBSVhcICopCiAgICAgICAgICAgIE5WTV9PUz1haXgKICAgICAgICA7OwogICAg
ICAgIENZR1dJTiogfCBNU1lTKiB8IE1JTkdXKikKICAgICAgICAgICAgTlZNX09TPXdpbgogICAg
ICAgIDs7CiAgICBlc2FjOwogICAgbnZtX2VjaG8gIiR7TlZNX09TLX0iCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2dyZXAgKCkgCnsgCiAgICBHUkVQX09QVElPTlM9JycgY29tbWFuZCBncmVwICIkQCIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhcyAoKSAKeyAKICAgIHR5cGUgIiR7MS19IiA+IC9kZXYvbnVsbCAyPiYxCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhc19jb2xvcnMgKCkgCnsgCiAgICBsb2NhbCBOVk1fTlVNX0NPTE9SUzsKICAgIGlmIG52
bV9oYXMgdHB1dDsgdGhlbgogICAgICAgIE5WTV9OVU1fQ09MT1JTPSIkKGNvbW1hbmQgdHB1dCAt
VCAiJHtURVJNOi12dDEwMH0iIGNvbG9ycykiOwogICAgZmk7CiAgICBbICIke05WTV9OVU1fQ09M
T1JTOi0tMX0iIC1nZSA4IF0gJiYgWyAiJHtOVk1fTk9fQ09MT1JTLX0iICE9ICctLW5vLWNvbG9y
cycgXQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhc19ub25fYWxpYXNlZCAoKSAKeyAKICAgIG52bV9oYXMgIiR7MS19IiAmJiAhIG52bV9p
c19hbGlhcyAiJHsxLX0iCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhc19zb2xhcmlzX2JpbmFyeSAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT049IiR7MS19IjsK
ICAgIGlmIG52bV9pc19tZXJnZWRfbm9kZV92ZXJzaW9uICIke1ZFUlNJT059IjsgdGhlbgogICAg
ICAgIHJldHVybiAwOwogICAgZWxzZQogICAgICAgIGlmIG52bV9pc19pb2pzX3ZlcnNpb24gIiR7
VkVSU0lPTn0iOyB0aGVuCiAgICAgICAgICAgIG52bV9pb2pzX3ZlcnNpb25faGFzX3NvbGFyaXNf
YmluYXJ5ICIke1ZFUlNJT059IjsKICAgICAgICBlbHNlCiAgICAgICAgICAgIG52bV9ub2RlX3Zl
cnNpb25faGFzX3NvbGFyaXNfYmluYXJ5ICIke1ZFUlNJT059IjsKICAgICAgICBmaTsKICAgIGZp
Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhc19zeXN0ZW1faW9qcyAoKSAKeyAKICAgIFsgIiQobnZtIGRlYWN0aXZhdGUgPiAvZGV2
L251bGwgMj4mMSAmJiBjb21tYW5kIC12IGlvanMpIiAhPSAnJyBdCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2hhc19zeXN0ZW1fbm9kZSAoKSAKeyAKICAgIFsgIiQobnZtIGRlYWN0aXZhdGUgPiAvZGV2
L251bGwgMj4mMSAmJiBjb21tYW5kIC12IG5vZGUpIiAhPSAnJyBdCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfYmluYXJ5ICgpIAp7IAogICAgbG9jYWwgRkxBVk9SOwogICAgY2FzZSAiJHsx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==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfYmluYXJ5X2V4dHJhY3QgKCkgCnsgCiAgICBpZiBbICIkIyIgLW5lIDUgXTsg
dGhlbgogICAgICAgIG52bV9lcnIgJ252bV9pbnN0YWxsX2JpbmFyeV9leHRyYWN0IG5lZWRzIDUg
cGFyYW1ldGVycyc7CiAgICAgICAgcmV0dXJuIDE7CiAgICBmaTsKICAgIGxvY2FsIE5WTV9PUzsK
ICAgIGxvY2FsIFBSRUZJWEVEX1ZFUlNJT047CiAgICBsb2NhbCBWRVJTSU9OOwogICAgbG9jYWwg
VEFSQkFMTDsKICAgIGxvY2FsIFRNUERJUjsKICAgIE5WTV9PUz0iJHsxfSI7CiAgICBQUkVGSVhF
RF9WRVJTSU9OPSIkezJ9IjsKICAgIFZFUlNJT049IiR7M30iOwogICAgVEFSQkFMTD0iJHs0fSI7
CiAgICBUTVBESVI9IiR7NX0iOwogICAgbG9jYWwgVkVSU0lPTl9QQVRIOwogICAgWyAtbiAiJHtU
TVBESVItfSIgXSAmJiBjb21tYW5kIG1rZGlyIC1wICIke1RNUERJUn0iICYmIFZFUlNJT05fUEFU
SD0iJChudm1fdmVyc2lvbl9wYXRoICIke1BSRUZJWEVEX1ZFUlNJT059IikiIHx8IHJldHVybiAx
OwogICAgaWYgWyAiJHtOVk1fT1N9IiA9ICd3aW4nIF07IHRoZW4KICAgICAgICBWRVJTSU9OX1BB
VEg9IiR7VkVSU0lPTl9QQVRIfS9iaW4iOwogICAgICAgIGNvbW1hbmQgdW56aXAgLXEgIiR7VEFS
QkFMTH0iIC1kICIke1RNUERJUn0iIHx8IHJldHVybiAxOwogICAgZWxzZQogICAgICAgIG52bV9l
eHRyYWN0X3RhcmJhbGwgIiR7TlZNX09TfSIgIiR7VkVSU0lPTn0iICIke1RBUkJBTEx9IiAiJHtU
TVBESVJ9IjsKICAgIGZpOwogICAgY29tbWFuZCBta2RpciAtcCAiJHtWRVJTSU9OX1BBVEh9IiB8
fCByZXR1cm4gMTsKICAgIGlmIFsgIiR7TlZNX09TfSIgPSAnd2luJyBdOyB0aGVuCiAgICAgICAg
Y29tbWFuZCBtdiAiJHtUTVBESVJ9LyIqLyogIiR7VkVSU0lPTl9QQVRIfS8iIHx8IHJldHVybiAx
OwogICAgICAgIGNvbW1hbmQgY2htb2QgK3ggIiR7VkVSU0lPTl9QQVRIfSIvbm9kZS5leGUgfHwg
cmV0dXJuIDE7CiAgICAgICAgY29tbWFuZCBjaG1vZCAreCAiJHtWRVJTSU9OX1BBVEh9Ii9ucG0g
fHwgcmV0dXJuIDE7CiAgICAgICAgY29tbWFuZCBjaG1vZCAreCAiJHtWRVJTSU9OX1BBVEh9Ii9u
cHggMj4gL2Rldi9udWxsOwogICAgZWxzZQogICAgICAgIGNvbW1hbmQgbXYgIiR7VE1QRElSfS8i
KiAiJHtWRVJTSU9OX1BBVEh9IiB8fCByZXR1cm4gMTsKICAgIGZpOwogICAgY29tbWFuZCBybSAt
cmYgIiR7VE1QRElSfSI7CiAgICByZXR1cm4gMAp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfZGVmYXVsdF9wYWNrYWdlcyAoKSAKeyAKICAgIGxvY2FsIERFRkFVTFRfUEFD
S0FHRVM7CiAgICBERUZBVUxUX1BBQ0tBR0VTPSIkKG52bV9nZXRfZGVmYXVsdF9wYWNrYWdlcyki
OwogICAgRVhJVF9DT0RFPSQ/OwogICAgaWYgWyAkRVhJVF9DT0RFIC1uZSAwIF0gfHwgWyAteiAi
JHtERUZBVUxUX1BBQ0tBR0VTfSIgXTsgdGhlbgogICAgICAgIHJldHVybiAkRVhJVF9DT0RFOwog
ICAgZmk7CiAgICBudm1fZWNobyAiSW5zdGFsbGluZyBkZWZhdWx0IGdsb2JhbCBwYWNrYWdlcyBm
cm9tICR7TlZNX0RJUn0vZGVmYXVsdC1wYWNrYWdlcy4uLiI7CiAgICBudm1fZWNobyAibnBtIGlu
c3RhbGwgLWcgLS1xdWlldCAke0RFRkFVTFRfUEFDS0FHRVN9IjsKICAgIGlmICEgbnZtX2VjaG8g
IiR7REVGQVVMVF9QQUNLQUdFU30iIHwgY29tbWFuZCB4YXJncyBucG0gaW5zdGFsbCAtZyAtLXF1
aWV0OyB0aGVuCiAgICAgICAgbnZtX2VyciAiRmFpbGVkIGluc3RhbGxpbmcgZGVmYXVsdCBwYWNr
YWdlcy4gUGxlYXNlIGNoZWNrIGlmIHlvdXIgZGVmYXVsdC1wYWNrYWdlcyBmaWxlIG9yIGEgcGFj
a2FnZSBpbiBpdCBoYXMgcHJvYmxlbXMhIjsKICAgICAgICByZXR1cm4gMTsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfbGF0ZXN0X25wbSAoKSAKeyAKICAgIG52bV9lY2hvICdBdHRlbXB0aW5nIHRv
IHVwZ3JhZGUgdG8gdGhlIGxhdGVzdCB3b3JraW5nIHZlcnNpb24gb2YgbnBtLi4uJzsKICAgIGxv
Y2FsIE5PREVfVkVSU0lPTjsKICAgIE5PREVfVkVSU0lPTj0iJChudm1fc3RyaXBfaW9qc19wcmVm
aXggIiQobnZtX2xzX2N1cnJlbnQpIikiOwogICAgbG9jYWwgTlBNX1ZFUlNJT047CiAgICBOUE1f
VkVSU0lPTj0iJChucG0gLS12ZXJzaW9uIDI+IC9kZXYvbnVsbCkiOwogICAgaWYgWyAiJHtOT0RF
X1ZFUlNJT059IiA9ICdzeXN0ZW0nIF07IHRoZW4KICAgICAgICBOT0RFX1ZFUlNJT049IiQobm9k
ZSAtLXZlcnNpb24pIjsKICAgIGVsc2UKICAgICAgICBpZiBbICIke05PREVfVkVSU0lPTn0iID0g
J25vbmUnIF07IHRoZW4KICAgICAgICAgICAgbnZtX2VjaG8gIkRldGVjdGVkIG5vZGUgdmVyc2lv
biAke05PREVfVkVSU0lPTn0sIG5wbSB2ZXJzaW9uIHYke05QTV9WRVJTSU9OfSI7CiAgICAgICAg
ICAgIE5PREVfVkVSU0lPTj0nJzsKICAgICAgICBmaTsKICAgIGZpOwogICAgaWYgWyAteiAiJHtO
T0RFX1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAgbnZtX2VyciAnVW5hYmxlIHRvIG9idGFpbiBu
b2RlIHZlcnNpb24uJzsKICAgICAgICByZXR1cm4gMTsKICAgIGZpOwogICAgaWYgWyAteiAiJHtO
UE1fVkVSU0lPTn0iIF07IHRoZW4KICAgICAgICBudm1fZXJyICdVbmFibGUgdG8gb2J0YWluIG5w
bSB2ZXJzaW9uLic7CiAgICAgICAgcmV0dXJuIDI7CiAgICBmaTsKICAgIGxvY2FsIE5WTV9OUE1f
Q01EOwogICAgTlZNX05QTV9DTUQ9J25wbSc7CiAgICBpZiBbICIke05WTV9ERUJVRy19IiA9IDEg
XTsgdGhlbgogICAgICAgIG52bV9lY2hvICJEZXRlY3RlZCBub2RlIHZlcnNpb24gJHtOT0RFX1ZF
UlNJT059LCBucG0gdmVyc2lvbiB2JHtOUE1fVkVSU0lPTn0iOwogICAgICAgIE5WTV9OUE1fQ01E
PSdudm1fZWNobyBucG0nOwogICAgZmk7CiAgICBsb2NhbCBOVk1fSVNfMF82OwogICAgTlZNX0lT
XzBfNj0wOwogICAgaWYgbnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05P
REVfVkVSU0lPTn0iIDAuNi4wICYmIG52bV92ZXJzaW9uX2dyZWF0ZXIgMC43LjAgIiR7Tk9ERV9W
RVJTSU9OfSI7IHRoZW4KICAgICAgICBOVk1fSVNfMF82PTE7CiAgICBmaTsKICAgIGxvY2FsIE5W
TV9JU18wXzk7CiAgICBOVk1fSVNfMF85PTA7CiAgICBpZiBudm1fdmVyc2lvbl9ncmVhdGVyX3Ro
YW5fb3JfZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMC45LjAgJiYgbnZtX3ZlcnNpb25fZ3Jl
YXRlciAwLjEwLjAgIiR7Tk9ERV9WRVJTSU9OfSI7IHRoZW4KICAgICAgICBOVk1fSVNfMF85PTE7
CiAgICBmaTsKICAgIGlmIFsgJE5WTV9JU18wXzYgLWVxIDEgXTsgdGhlbgogICAgICAgIG52bV9l
Y2hvICcqIGBub2RlYCB2MC42LnggY2FuIG9ubHkgdXBncmFkZSB0byBgbnBtYCB2MS4zLngnOwog
ICAgICAgICROVk1fTlBNX0NNRCBpbnN0YWxsIC1nIG5wbUAxLjM7CiAgICBlbHNlCiAgICAgICAg
aWYgWyAkTlZNX0lTXzBfOSAtZXEgMCBdOyB0aGVuCiAgICAgICAgICAgIGlmIG52bV92ZXJzaW9u
X2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOUE1fVkVSU0lPTn0iIDEuMC4wICYmIG52bV92
ZXJzaW9uX2dyZWF0ZXIgMi4wLjAgIiR7TlBNX1ZFUlNJT059IjsgdGhlbgogICAgICAgICAgICAg
ICAgbnZtX2VjaG8gJyogYG5wbWAgdjEueCBuZWVkcyB0byBmaXJzdCBqdW1wIHRvIGBucG1gIHYx
LjQuMjggdG8gYmUgYWJsZSB0byB1cGdyYWRlIGZ1cnRoZXInOwogICAgICAgICAgICAgICAgJE5W
TV9OUE1fQ01EIGluc3RhbGwgLWcgbnBtQDEuNC4yODsKICAgICAgICAgICAgZWxzZQogICAgICAg
ICAgICAgICAgaWYgbnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05QTV9W
RVJTSU9OfSIgMi4wLjAgJiYgbnZtX3ZlcnNpb25fZ3JlYXRlciAzLjAuMCAiJHtOUE1fVkVSU0lP
Tn0iOyB0aGVuCiAgICAgICAgICAgICAgICAgICAgbnZtX2VjaG8gJyogYG5wbWAgdjIueCBuZWVk
cyB0byBmaXJzdCBqdW1wIHRvIHRoZSBsYXRlc3QgdjIgdG8gYmUgYWJsZSB0byB1cGdyYWRlIGZ1
cnRoZXInOwogICAgICAgICAgICAgICAgICAgICROVk1fTlBNX0NNRCBpbnN0YWxsIC1nIG5wbUAy
OwogICAgICAgICAgICAgICAgZmk7CiAgICAgICAgICAgIGZpOwogICAgICAgIGZpOwogICAgZmk7
CiAgICBpZiBbICROVk1fSVNfMF85IC1lcSAxIF0gfHwgWyAkTlZNX0lTXzBfNiAtZXEgMSBdOyB0
aGVuCiAgICAgICAgbnZtX2VjaG8gJyogbm9kZSB2MC42IGFuZCB2MC45IGFyZSB1bmFibGUgdG8g
dXBncmFkZSBmdXJ0aGVyJzsKICAgIGVsc2UKICAgICAgICBpZiBudm1fdmVyc2lvbl9ncmVhdGVy
IDEuMS4wICIke05PREVfVkVSU0lPTn0iOyB0aGVuCiAgICAgICAgICAgIG52bV9lY2hvICcqIGBu
cG1gIHY0LjUueCBpcyB0aGUgbGFzdCB2ZXJzaW9uIHRoYXQgd29ya3Mgb24gYG5vZGVgIHZlcnNp
b25zIDwgdjEuMS4wJzsKICAgICAgICAgICAgJE5WTV9OUE1fQ01EIGluc3RhbGwgLWcgbnBtQDQu
NTsKICAgICAgICBlbHNlCiAgICAgICAgICAgIGlmIG52bV92ZXJzaW9uX2dyZWF0ZXIgNC4wLjAg
IiR7Tk9ERV9WRVJTSU9OfSI7IHRoZW4KICAgICAgICAgICAgICAgIG52bV9lY2hvICcqIGBucG1g
IHY1IGFuZCBoaWdoZXIgZG8gbm90IHdvcmsgb24gYG5vZGVgIHZlcnNpb25zIGJlbG93IHY0LjAu
MCc7CiAgICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFsbCAtZyBucG1ANDsKICAgICAg
ICAgICAgZWxzZQogICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzBfOSAtZXEgMCBdICYmIFsg
JE5WTV9JU18wXzYgLWVxIDAgXTsgdGhlbgogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9J
U180XzRfT1JfQkVMT1c7CiAgICAgICAgICAgICAgICAgICAgTlZNX0lTXzRfNF9PUl9CRUxPVz0w
OwogICAgICAgICAgICAgICAgICAgIGlmIG52bV92ZXJzaW9uX2dyZWF0ZXIgNC41LjAgIiR7Tk9E
RV9WRVJTSU9OfSI7IHRoZW4KICAgICAgICAgICAgICAgICAgICAgICAgTlZNX0lTXzRfNF9PUl9C
RUxPVz0xOwogICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2Fs
IE5WTV9JU181X09SX0FCT1ZFOwogICAgICAgICAgICAgICAgICAgIE5WTV9JU181X09SX0FCT1ZF
PTA7CiAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzRfNF9PUl9CRUxPVyAtZXEgMCBd
ICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059
IiA1LjAuMDsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICBOVk1fSVNfNV9PUl9BQk9WRT0x
OwogICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9J
U182X09SX0FCT1ZFOwogICAgICAgICAgICAgICAgICAgIE5WTV9JU182X09SX0FCT1ZFPTA7CiAg
ICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lTXzZfMl9PUl9BQk9WRTsKICAgICAgICAgICAg
ICAgICAgICBOVk1fSVNfNl8yX09SX0FCT1ZFPTA7CiAgICAgICAgICAgICAgICAgICAgaWYgWyAk
TlZNX0lTXzVfT1JfQUJPVkUgLWVxIDEgXSAmJiBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3Jf
ZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgNi4wLjA7IHRoZW4KICAgICAgICAgICAgICAgICAg
ICAgICAgTlZNX0lTXzZfT1JfQUJPVkU9MTsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgbnZt
X3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05PREVfVkVSU0lPTn0iIDYuMi4w
OyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBOVk1fSVNfNl8yX09SX0FCT1ZFPTE7
CiAgICAgICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGZpOwogICAg
ICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU185X09SX0FCT1ZFOwogICAgICAgICAgICAgICAg
ICAgIE5WTV9JU185X09SX0FCT1ZFPTA7CiAgICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lT
XzlfM19PUl9BQk9WRTsKICAgICAgICAgICAgICAgICAgICBOVk1fSVNfOV8zX09SX0FCT1ZFPTA7
CiAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzZfMl9PUl9BQk9WRSAtZXEgMSBdICYm
IG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiA5
LjAuMDsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICBOVk1fSVNfOV9PUl9BQk9WRT0xOwog
ICAgICAgICAgICAgICAgICAgICAgICBpZiBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1
YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgOS4zLjA7IHRoZW4KICAgICAgICAgICAgICAgICAgICAg
ICAgICAgIE5WTV9JU185XzNfT1JfQUJPVkU9MTsKICAgICAgICAgICAgICAgICAgICAgICAgZmk7
CiAgICAgICAgICAgICAgICAgICAgZmk7CiAgICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lT
XzEwX09SX0FCT1ZFOwogICAgICAgICAgICAgICAgICAgIE5WTV9JU18xMF9PUl9BQk9WRT0wOwog
ICAgICAgICAgICAgICAgICAgIGlmIFsgJE5WTV9JU185XzNfT1JfQUJPVkUgLWVxIDEgXSAmJiBu
dm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMTAu
MC4wOyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgIE5WTV9JU18xMF9PUl9BQk9WRT0xOwog
ICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18x
Ml9MVFNfT1JfQUJPVkU7CiAgICAgICAgICAgICAgICAgICAgTlZNX0lTXzEyX0xUU19PUl9BQk9W
RT0wOwogICAgICAgICAgICAgICAgICAgIGlmIFsgJE5WTV9JU18xMF9PUl9BQk9WRSAtZXEgMSBd
ICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059
IiAxMi4xMy4wOyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgIE5WTV9JU18xMl9MVFNfT1Jf
QUJPVkU9MTsKICAgICAgICAgICAgICAgICAgICBmaTsKICAgICAgICAgICAgICAgICAgICBsb2Nh
bCBOVk1fSVNfMTNfT1JfQUJPVkU7CiAgICAgICAgICAgICAgICAgICAgTlZNX0lTXzEzX09SX0FC
T1ZFPTA7CiAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzEyX0xUU19PUl9BQk9WRSAt
ZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZF
UlNJT059IiAxMy4wLjA7IHRoZW4KICAgICAgICAgICAgICAgICAgICAgICAgTlZNX0lTXzEzX09S
X0FCT1ZFPTE7CiAgICAgICAgICAgICAgICAgICAgZmk7CiAgICAgICAgICAgICAgICAgICAgbG9j
YWwgTlZNX0lTXzE0X0xUU19PUl9BQk9WRTsKICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMTRf
TFRTX09SX0FCT1ZFPTA7CiAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzEzX09SX0FC
T1ZFIC1lcSAxIF0gJiYgbnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05P
REVfVkVSU0lPTn0iIDE0LjE1LjA7IHRoZW4KICAgICAgICAgICAgICAgICAgICAgICAgTlZNX0lT
XzE0X0xUU19PUl9BQk9WRT0xOwogICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAg
ICAgICAgIGxvY2FsIE5WTV9JU18xNF8xN19PUl9BQk9WRTsKICAgICAgICAgICAgICAgICAgICBO
Vk1fSVNfMTRfMTdfT1JfQUJPVkU9MDsKICAgICAgICAgICAgICAgICAgICBpZiBbICROVk1fSVNf
MTRfTFRTX09SX0FCT1ZFIC1lcSAxIF0gJiYgbnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2Vx
dWFsX3RvICIke05PREVfVkVSU0lPTn0iIDE0LjE3LjA7IHRoZW4KICAgICAgICAgICAgICAgICAg
ICAgICAgTlZNX0lTXzE0XzE3X09SX0FCT1ZFPTE7CiAgICAgICAgICAgICAgICAgICAgZmk7CiAg
ICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lTXzE1X09SX0FCT1ZFOwogICAgICAgICAgICAg
ICAgICAgIE5WTV9JU18xNV9PUl9BQk9WRT0wOwogICAgICAgICAgICAgICAgICAgIGlmIFsgJE5W
TV9JU18xNF9MVFNfT1JfQUJPVkUgLWVxIDEgXSAmJiBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5f
b3JfZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMTUuMC4wOyB0aGVuCiAgICAgICAgICAgICAg
ICAgICAgICAgIE5WTV9JU18xNV9PUl9BQk9WRT0xOwogICAgICAgICAgICAgICAgICAgIGZpOwog
ICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18xNl9PUl9BQk9WRTsKICAgICAgICAgICAg
ICAgICAgICBOVk1fSVNfMTZfT1JfQUJPVkU9MDsKICAgICAgICAgICAgICAgICAgICBpZiBbICRO
Vk1fSVNfMTVfT1JfQUJPVkUgLWVxIDEgXSAmJiBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3Jf
ZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMTYuMC4wOyB0aGVuCiAgICAgICAgICAgICAgICAg
ICAgICAgIE5WTV9JU18xNl9PUl9BQk9WRT0xOwogICAgICAgICAgICAgICAgICAgIGZpOwogICAg
ICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18xNl9MVFNfT1JfQUJPVkU7CiAgICAgICAgICAg
ICAgICAgICAgTlZNX0lTXzE2X0xUU19PUl9BQk9WRT0wOwogICAgICAgICAgICAgICAgICAgIGlm
IFsgJE5WTV9JU18xNl9PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhh
bl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiAxNi4xMy4wOyB0aGVuCiAgICAgICAgICAg
ICAgICAgICAgICAgIE5WTV9JU18xNl9MVFNfT1JfQUJPVkU9MTsKICAgICAgICAgICAgICAgICAg
ICBmaTsKICAgICAgICAgICAgICAgICAgICBsb2NhbCBOVk1fSVNfMTdfT1JfQUJPVkU7CiAgICAg
ICAgICAgICAgICAgICAgTlZNX0lTXzE3X09SX0FCT1ZFPTA7CiAgICAgICAgICAgICAgICAgICAg
aWYgWyAkTlZNX0lTXzE2X0xUU19PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0
ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiAxNy4wLjA7IHRoZW4KICAgICAg
ICAgICAgICAgICAgICAgICAgTlZNX0lTXzE3X09SX0FCT1ZFPTE7CiAgICAgICAgICAgICAgICAg
ICAgZmk7CiAgICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lTXzE4X09SX0FCT1ZFOwogICAg
ICAgICAgICAgICAgICAgIE5WTV9JU18xOF9PUl9BQk9WRT0wOwogICAgICAgICAgICAgICAgICAg
IGlmIFsgJE5WTV9JU18xN19PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0ZXJf
dGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiAxOC4wLjA7IHRoZW4KICAgICAgICAg
ICAgICAgICAgICAgICAgTlZNX0lTXzE4X09SX0FCT1ZFPTE7CiAgICAgICAgICAgICAgICAgICAg
Zmk7CiAgICAgICAgICAgICAgICAgICAgbG9jYWwgTlZNX0lTXzE4XzE3X09SX0FCT1ZFOwogICAg
ICAgICAgICAgICAgICAgIE5WTV9JU18xOF8xN19PUl9BQk9WRT0wOwogICAgICAgICAgICAgICAg
ICAgIGlmIFsgJE5WTV9JU18xOF9PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0
ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiAxOC4xNy4wOyB0aGVuCiAgICAg
ICAgICAgICAgICAgICAgICAgIE5WTV9JU18xOF8xN19PUl9BQk9WRT0xOwogICAgICAgICAgICAg
ICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18xOV9PUl9BQk9WRTsK
ICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMTlfT1JfQUJPVkU9MDsKICAgICAgICAgICAgICAg
ICAgICBpZiBbICROVk1fSVNfMThfMTdfT1JfQUJPVkUgLWVxIDEgXSAmJiBudm1fdmVyc2lvbl9n
cmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMTkuMC4wOyB0aGVuCiAg
ICAgICAgICAgICAgICAgICAgICAgIE5WTV9JU18xOV9PUl9BQk9WRT0xOwogICAgICAgICAgICAg
ICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18yMF81X09SX0FCT1ZF
OwogICAgICAgICAgICAgICAgICAgIE5WTV9JU18yMF81X09SX0FCT1ZFPTA7CiAgICAgICAgICAg
ICAgICAgICAgaWYgWyAkTlZNX0lTXzE5X09SX0FCT1ZFIC1lcSAxIF0gJiYgbnZtX3ZlcnNpb25f
Z3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05PREVfVkVSU0lPTn0iIDIwLjUuMDsgdGhlbgog
ICAgICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMjBfNV9PUl9BQk9WRT0xOwogICAgICAgICAg
ICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18yMF8xN19PUl9B
Qk9WRTsKICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMjBfMTdfT1JfQUJPVkU9MDsKICAgICAg
ICAgICAgICAgICAgICBpZiBbICROVk1fSVNfMjBfNV9PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92
ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJT059IiAyMC4xNy4w
OyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgIE5WTV9JU18yMF8xN19PUl9BQk9WRT0xOwog
ICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18y
MV9PUl9BQk9WRTsKICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMjFfT1JfQUJPVkU9MDsKICAg
ICAgICAgICAgICAgICAgICBpZiBbICROVk1fSVNfMjBfMTdfT1JfQUJPVkUgLWVxIDEgXSAmJiBu
dm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7Tk9ERV9WRVJTSU9OfSIgMjEu
MC4wOyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgIE5WTV9JU18yMV9PUl9BQk9WRT0xOwog
ICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGxvY2FsIE5WTV9JU18y
Ml85X09SX0FCT1ZFOwogICAgICAgICAgICAgICAgICAgIE5WTV9JU18yMl85X09SX0FCT1ZFPTA7
CiAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzIxX09SX0FCT1ZFIC1lcSAxIF0gJiYg
bnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke05PREVfVkVSU0lPTn0iIDIy
LjkuMDsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICBOVk1fSVNfMjJfOV9PUl9BQk9WRT0x
OwogICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgIGlmIFsgJE5WTV9J
U180XzRfT1JfQkVMT1cgLWVxIDEgXSB8fCB7IAogICAgICAgICAgICAgICAgICAgICAgICBbICRO
Vk1fSVNfNV9PUl9BQk9WRSAtZXEgMSBdICYmIG52bV92ZXJzaW9uX2dyZWF0ZXIgNS4xMC4wICIk
e05PREVfVkVSU0lPTn0iCiAgICAgICAgICAgICAgICAgICAgfTsgdGhlbgogICAgICAgICAgICAg
ICAgICAgICAgICBudm1fZWNobyAnKiBgbnBtYCBgdjUuMy54YCBpcyB0aGUgbGFzdCB2ZXJzaW9u
IHRoYXQgd29ya3Mgb24gYG5vZGVgIDQueCB2ZXJzaW9ucyBiZWxvdyB2NC40LCBvciA1LnggdmVy
c2lvbnMgYmVsb3cgdjUuMTAsIGR1ZSB0byBgQnVmZmVyLmFsbG9jYCc7CiAgICAgICAgICAgICAg
ICAgICAgICAgICROVk1fTlBNX0NNRCBpbnN0YWxsIC1nIG5wbUA1LjM7CiAgICAgICAgICAgICAg
ICAgICAgZWxzZQogICAgICAgICAgICAgICAgICAgICAgICBpZiBbICROVk1fSVNfNF80X09SX0JF
TE9XIC1lcSAwIF0gJiYgbnZtX3ZlcnNpb25fZ3JlYXRlciA0LjcuMCAiJHtOT0RFX1ZFUlNJT059
IjsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnZtX2VjaG8gJyogYG5wbWAgYHY1
LjQuMWAgaXMgdGhlIGxhc3QgdmVyc2lvbiB0aGF0IHdvcmtzIG9uIGBub2RlYCBgdjQuNWAgYW5k
IGB2NC42YCc7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFs
bCAtZyBucG1ANS40LjE7CiAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAg
ICAgICAgICAgICAgICAgIGlmIFsgJE5WTV9JU182X09SX0FCT1ZFIC1lcSAwIF07IHRoZW4KICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudm1fZWNobyAnKiBgbnBtYCBgdjUueGAgaXMg
dGhlIGxhc3QgdmVyc2lvbiB0aGF0IHdvcmtzIG9uIGBub2RlYCBiZWxvdyBgdjYuMC4wYCc7CiAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJE5WTV9OUE1fQ01EIGluc3RhbGwgLWcgbnBt
QDU7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlCiAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgaWYgeyAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWyAk
TlZNX0lTXzZfT1JfQUJPVkUgLWVxIDEgXSAmJiBbICROVk1fSVNfNl8yX09SX0FCT1ZFIC1lcSAw
IF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IHx8IHsgCiAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgIFsgJE5WTV9JU185X09SX0FCT1ZFIC1lcSAxIF0gJiYgWyAk
TlZNX0lTXzlfM19PUl9BQk9WRSAtZXEgMCBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgfTsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudm1fZWNobyAn
KiBgbnBtYCBgdjYuOWAgaXMgdGhlIGxhc3QgdmVyc2lvbiB0aGF0IHdvcmtzIG9uIGBub2RlYCBg
djYuMC54YCwgYHY2LjEueGAsIGB2OS4wLnhgLCBgdjkuMS54YCwgb3IgYHY5LjIueGAnOwogICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFsbCAtZyBu
cG1ANi45OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzEwX09SX0FCT1ZFIC1lcSAwIF07
IHRoZW4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIG52bV92ZXJz
aW9uX2dyZWF0ZXIgNC40LjQgIiR7TlBNX1ZFUlNJT059IjsgdGhlbgogICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgIG52bV9lY2hvICcqIGBucG1gIGB2NC40LjRgIG9y
IGxhdGVyIGlzIHJlcXVpcmVkIHRvIGluc3RhbGwgbnBtIHY2LjE0LjE4JzsKICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFsbCAtZyBu
cG1ANDsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnZtX2VjaG8gJyogYG5wbWAgYHY2Lnhg
IGlzIHRoZSBsYXN0IHZlcnNpb24gdGhhdCB3b3JrcyBvbiBgbm9kZWAgYmVsb3cgYHYxMC4wLjBg
JzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICROVk1fTlBNX0NNRCBp
bnN0YWxsIC1nIG5wbUA2OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNl
CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiBbICROVk1fSVNfMTJf
TFRTX09SX0FCT1ZFIC1lcSAwIF0gfHwgeyAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICBbICROVk1fSVNfMTNfT1JfQUJPVkUgLWVxIDEgXSAmJiBbICROVk1fSVNf
MTRfTFRTX09SX0FCT1ZFIC1lcSAwIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgIH0gfHwgeyAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICBbICROVk1fSVNfMTVfT1JfQUJPVkUgLWVxIDEgXSAmJiBbICROVk1fSVNfMTZfT1JfQUJPVkUg
LWVxIDAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTsgdGhlbgog
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG52bV9lY2hvICcqIGBu
cG1gIGB2Ny54YCBpcyB0aGUgbGFzdCB2ZXJzaW9uIHRoYXQgd29ya3Mgb24gYG5vZGVgIGB2MTNg
LCBgdjE1YCwgYmVsb3cgYHYxMi4xM2AsIG9yIGB2MTQuMGAgLSBgdjE0LjE1YCc7CiAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJE5WTV9OUE1fQ01EIGluc3RhbGwg
LWcgbnBtQDc7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlCiAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgeyAKICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWyAkTlZNX0lTXzEyX0xUU19P
Ul9BQk9WRSAtZXEgMSBdICYmIFsgJE5WTV9JU18xM19PUl9BQk9WRSAtZXEgMCBdCiAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSB8fCB7IAogICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbICROVk1fSVNfMTRfTFRTX09SX0FC
T1ZFIC1lcSAxIF0gJiYgWyAkTlZNX0lTXzE0XzE3X09SX0FCT1ZFIC1lcSAwIF0KICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IHx8IHsgCiAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsgJE5WTV9JU18xNl9PUl9BQk9WRSAt
ZXEgMSBdICYmIFsgJE5WTV9JU18xNl9MVFNfT1JfQUJPVkUgLWVxIDAgXQogICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gfHwgeyAKICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWyAkTlZNX0lTXzE3X09SX0FCT1ZFIC1lcSAx
IF0gJiYgWyAkTlZNX0lTXzE4X09SX0FCT1ZFIC1lcSAwIF0KICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICB9OyB0aGVuCiAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgIG52bV9lY2hvICcqIGBucG1gIGB2OC42YCBpcyB0aGUgbGFz
dCB2ZXJzaW9uIHRoYXQgd29ya3Mgb24gYG5vZGVgIGB2MTJgLCBgdjE0LjEzYCAtIGB2MTQuMTZg
LCBvciBgdjE2LjBgIC0gYHYxNi4xMmAnOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFsbCAtZyBucG1AOC42OwogICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgWyAkTlZNX0lTXzE4XzE3X09SX0FC
T1ZFIC1lcSAwIF0gfHwgeyAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgIFsgJE5WTV9JU18xOV9PUl9BQk9WRSAtZXEgMSBdICYmIFsgJE5WTV9JU18y
MF81X09SX0FCT1ZFIC1lcSAwIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgfTsgdGhlbgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgbnZtX2VjaG8gJyogYG5wbWAgYHY5LnhgIGlzIHRoZSBsYXN0IHZlcnNp
b24gdGhhdCB3b3JrcyBvbiBgbm9kZWAgYDwgdjE4LjE3YCwgYHYxOWAsIG9yIGB2MjAuMGAgLSBg
djIwLjRgJzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICROVk1fTlBNX0NNRCBpbnN0YWxsIC1nIG5wbUA5OwogICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlCiAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICBpZiBbICROVk1fSVNfMjBfMTdfT1JfQUJPVkUgLWVx
IDAgXSB8fCB7IAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgIFsgJE5WTV9JU18yMV9PUl9BQk9WRSAtZXEgMSBdICYmIFsgJE5WTV9JU18yMl85
X09SX0FCT1ZFIC1lcSAwIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgIH07IHRoZW4KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICBudm1fZWNobyAnKiBgbnBtYCBgdjEwLnhgIGlzIHRoZSBsYXN0
IHZlcnNpb24gdGhhdCB3b3JrcyBvbiBgbm9kZWAgYDwgdjIwLjE3YCwgYHYyMWAsIG9yIGB2MjIu
MGAgLSBgdjIyLjhgJzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAkTlZNX05QTV9DTUQgaW5zdGFsbCAtZyBucG1AMTA7CiAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbHNlCiAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbnZtX2VjaG8gJyog
SW5zdGFsbGluZyBsYXRlc3QgYG5wbWA7IGlmIHRoaXMgZG9lcyBub3Qgd29yayBvbiB5b3VyIG5v
ZGUgdmVyc2lvbiwgcGxlYXNlIHJlcG9ydCBhIGJ1ZyEnOwogICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICROVk1fTlBNX0NNRCBpbnN0YWxsIC1n
IG5wbTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg
IGZpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaTsK
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaTsKICAgICAgICAg
ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgICAg
ICAgICAgICAgICAgICBmaTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaTsKICAg
ICAgICAgICAgICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICAgICAgICAgICAgICBmaTsK
ICAgICAgICAgICAgICAgICAgICBmaTsKICAgICAgICAgICAgICAgIGZpOwogICAgICAgICAgICBm
aTsKICAgICAgICBmaTsKICAgIGZpOwogICAgbnZtX2VjaG8gIiogbnBtIHVwZ3JhZGVkIHRvOiB2
JChucG0gLS12ZXJzaW9uIDI+IC9kZXYvbnVsbCkiCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfbnBtX2lmX25lZWRlZCAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT047CiAgICBW
RVJTSU9OPSIkKG52bV9sc19jdXJyZW50KSI7CiAgICBpZiAhIG52bV9oYXMgIm5wbSI7IHRoZW4K
ICAgICAgICBudm1fZWNobyAnSW5zdGFsbGluZyBucG0uLi4nOwogICAgICAgIGlmIG52bV92ZXJz
aW9uX2dyZWF0ZXIgMC4yLjAgIiR7VkVSU0lPTn0iOyB0aGVuCiAgICAgICAgICAgIG52bV9lcnIg
J25wbSByZXF1aXJlcyBub2RlIHYwLjIuMyBvciBoaWdoZXInOwogICAgICAgIGVsc2UKICAgICAg
ICAgICAgaWYgbnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIke1ZFUlNJT059
IiAwLjIuMDsgdGhlbgogICAgICAgICAgICAgICAgaWYgbnZtX3ZlcnNpb25fZ3JlYXRlciAwLjIu
MyAiJHtWRVJTSU9OfSI7IHRoZW4KICAgICAgICAgICAgICAgICAgICBudm1fZXJyICducG0gcmVx
dWlyZXMgbm9kZSB2MC4yLjMgb3IgaGlnaGVyJzsKICAgICAgICAgICAgICAgIGVsc2UKICAgICAg
ICAgICAgICAgICAgICBudm1fZG93bmxvYWQgLUwgaHR0cHM6Ly9ucG1qcy5vcmcvaW5zdGFsbC5z
aCAtbyAtIHwgY2xlYW49eWVzIG5wbV9pbnN0YWxsPTAuMi4xOSBzaDsKICAgICAgICAgICAgICAg
IGZpOwogICAgICAgICAgICBlbHNlCiAgICAgICAgICAgICAgICBudm1fZG93bmxvYWQgLUwgaHR0
cHM6Ly9ucG1qcy5vcmcvaW5zdGFsbC5zaCAtbyAtIHwgY2xlYW49eWVzIHNoOwogICAgICAgICAg
ICBmaTsKICAgICAgICBmaTsKICAgIGZpOwogICAgcmV0dXJuICQ/Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2luc3RhbGxfc291cmNlICgpIAp7IAogICAgbG9jYWwgRkxBVk9SOwogICAgY2FzZSAiJHsx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=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lvanNfcHJlZml4ICgpIAp7IAogICAgbnZtX2VjaG8gJ2lvanMnCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lvanNfdmVyc2lvbl9oYXNfc29sYXJpc19iaW5hcnkgKCkgCnsgCiAgICBsb2NhbCBJT0pT
X1ZFUlNJT047CiAgICBJT0pTX1ZFUlNJT049IiQxIjsKICAgIGxvY2FsIFNUUklQUEVEX0lPSlNf
VkVSU0lPTjsKICAgIFNUUklQUEVEX0lPSlNfVkVSU0lPTj0iJChudm1fc3RyaXBfaW9qc19wcmVm
aXggIiR7SU9KU19WRVJTSU9OfSIpIjsKICAgIGlmIFsgIl8ke1NUUklQUEVEX0lPSlNfVkVSU0lP
Tn0iID0gIiR7SU9KU19WRVJTSU9OfSIgXTsgdGhlbgogICAgICAgIHJldHVybiAxOwogICAgZmk7
CiAgICBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7U1RSSVBQRURfSU9K
U19WRVJTSU9OfSIgdjMuMy4xCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX2FsaWFzICgpIAp7IAogICAgXGFsaWFzICIkezEtfSIgPiAvZGV2L251bGwgMj4mMQp9
Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX2lvanNfdmVyc2lvbiAoKSAKeyAKICAgIGNhc2UgIiR7MS19IiBpbiAKICAgICAgICBp
b2pzLSopCiAgICAgICAgICAgIHJldHVybiAwCiAgICAgICAgOzsKICAgIGVzYWM7CiAgICByZXR1
cm4gMQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX21lcmdlZF9ub2RlX3ZlcnNpb24gKCkgCnsgCiAgICBudm1fdmVyc2lvbl9ncmVhdGVy
X3RoYW5fb3JfZXF1YWxfdG8gIiQxIiB2NC4wLjAKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX25hdHVyYWxfbnVtICgpIAp7IAogICAgaWYgWyAteiAiJDEiIF07IHRoZW4KICAgICAg
ICByZXR1cm4gNDsKICAgIGZpOwogICAgY2FzZSAiJDEiIGluIAogICAgICAgIDApCiAgICAgICAg
ICAgIHJldHVybiAxCiAgICAgICAgOzsKICAgICAgICAtKikKICAgICAgICAgICAgcmV0dXJuIDMK
ICAgICAgICA7OwogICAgICAgICopCiAgICAgICAgICAgIFsgIiQxIiAtZXEgIiQxIiBdIDI+IC9k
ZXYvbnVsbAogICAgICAgIDs7CiAgICBlc2FjCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX3ZhbGlkX3ZlcnNpb24gKCkgCnsgCiAgICBpZiBudm1fdmFsaWRhdGVfaW1wbGljaXRf
YWxpYXMgIiR7MS19IiAyPiAvZGV2L251bGw7IHRoZW4KICAgICAgICByZXR1cm4gMDsKICAgIGZp
OwogICAgY2FzZSAiJHsxLX0iIGluIAogICAgICAgICIkKG52bV9pb2pzX3ByZWZpeCkiIHwgIiQo
bnZtX25vZGVfcHJlZml4KSIpCiAgICAgICAgICAgIHJldHVybiAwCiAgICAgICAgOzsKICAgICAg
ICAqKQogICAgICAgICAgICBsb2NhbCBWRVJTSU9OOwogICAgICAgICAgICBWRVJTSU9OPSIkKG52
bV9zdHJpcF9pb2pzX3ByZWZpeCAiJHsxLX0iKSI7CiAgICAgICAgICAgIG52bV92ZXJzaW9uX2dy
ZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtWRVJTSU9OfSIgMAogICAgICAgIDs7CiAgICBlc2Fj
Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX3ZlcnNpb25faW5zdGFsbGVkICgpIAp7IAogICAgaWYgWyAteiAiJHsxLX0iIF07IHRo
ZW4KICAgICAgICByZXR1cm4gMTsKICAgIGZpOwogICAgbG9jYWwgTlZNX05PREVfQklOQVJZOwog
ICAgTlZNX05PREVfQklOQVJZPSdub2RlJzsKICAgIGlmIFsgIl8kKG52bV9nZXRfb3MpIiA9ICdf
d2luJyBdOyB0aGVuCiAgICAgICAgTlZNX05PREVfQklOQVJZPSdub2RlLmV4ZSc7CiAgICBmaTsK
ICAgIGlmIFsgLXggIiQobnZtX3ZlcnNpb25fcGF0aCAiJDEiIDI+IC9kZXYvbnVsbCkvYmluLyR7
TlZNX05PREVfQklOQVJZfSIgXTsgdGhlbgogICAgICAgIHJldHVybiAwOwogICAgZmk7CiAgICBy
ZXR1cm4gMQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2lzX3pzaCAoKSAKeyAKICAgIFsgLW4gIiR7WlNIX1ZFUlNJT04tfSIgXQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xpc3RfYWxpYXNlcyAoKSAKeyAKICAgIGxvY2FsIEFMSUFTOwogICAgQUxJQVM9IiR7MS19
IjsKICAgIGxvY2FsIE5WTV9DVVJSRU5UOwogICAgTlZNX0NVUlJFTlQ9IiQobnZtX2xzX2N1cnJl
bnQpIjsKICAgIGxvY2FsIE5WTV9BTElBU19ESVI7CiAgICBOVk1fQUxJQVNfRElSPSIkKG52bV9h
bGlhc19wYXRoKSI7CiAgICBjb21tYW5kIG1rZGlyIC1wICIke05WTV9BTElBU19ESVJ9L2x0cyI7
CiAgICBpZiBbICIke0FMSUFTfSIgIT0gIiR7QUxJQVMjbHRzL30iIF07IHRoZW4KICAgICAgICBu
dm1fYWxpYXMgIiR7QUxJQVN9IjsKICAgICAgICByZXR1cm4gJD87CiAgICBmaTsKICAgIG52bV9p
c196c2ggJiYgdW5zZXRvcHQgbG9jYWxfb3B0aW9ucyBub21hdGNoOwogICAgKCBsb2NhbCBBTElB
U19QQVRIOwogICAgZm9yIEFMSUFTX1BBVEggaW4gIiR7TlZNX0FMSUFTX0RJUn0vJHtBTElBU30i
KjsKICAgIGRvCiAgICAgICAgTlZNX05PX0NPTE9SUz0iJHtOVk1fTk9fQ09MT1JTLX0iIE5WTV9D
VVJSRU5UPSIke05WTV9DVVJSRU5UfSIgbnZtX3ByaW50X2FsaWFzX3BhdGggIiR7TlZNX0FMSUFT
X0RJUn0iICIke0FMSUFTX1BBVEh9IiAmCiAgICBkb25lOwogICAgd2FpdCApIHwgY29tbWFuZCBz
b3J0OwogICAgKCBsb2NhbCBBTElBU19OQU1FOwogICAgZm9yIEFMSUFTX05BTUUgaW4gIiQobnZt
X25vZGVfcHJlZml4KSIgInN0YWJsZSIgInVuc3RhYmxlIiAiJChudm1faW9qc19wcmVmaXgpIjsK
ICAgIGRvCiAgICAgICAgeyAKICAgICAgICAgICAgaWYgWyAhIC1mICIke05WTV9BTElBU19ESVJ9
LyR7QUxJQVNfTkFNRX0iIF0gJiYgeyAKICAgICAgICAgICAgICAgIFsgLXogIiR7QUxJQVN9IiBd
IHx8IFsgIiR7QUxJQVNfTkFNRX0iID0gIiR7QUxJQVN9IiBdCiAgICAgICAgICAgIH07IHRoZW4K
ICAgICAgICAgICAgICAgIE5WTV9OT19DT0xPUlM9IiR7TlZNX05PX0NPTE9SUy19IiBOVk1fQ1VS
UkVOVD0iJHtOVk1fQ1VSUkVOVH0iIG52bV9wcmludF9kZWZhdWx0X2FsaWFzICIke0FMSUFTX05B
TUV9IjsKICAgICAgICAgICAgZmkKICAgICAgICB9ICYKICAgIGRvbmU7CiAgICB3YWl0ICkgfCBj
b21tYW5kIHNvcnQ7CiAgICAoIGxvY2FsIExUU19BTElBUzsKICAgIGZvciBBTElBU19QQVRIIGlu
ICIke05WTV9BTElBU19ESVJ9L2x0cy8ke0FMSUFTfSIqOwogICAgZG8KICAgICAgICB7IAogICAg
ICAgICAgICBMVFNfQUxJQVM9IiQoTlZNX05PX0NPTE9SUz0iJHtOVk1fTk9fQ09MT1JTLX0iIE5W
TV9MVFM9dHJ1ZSBudm1fcHJpbnRfYWxpYXNfcGF0aCAiJHtOVk1fQUxJQVNfRElSfSIgIiR7QUxJ
QVNfUEFUSH0iKSI7CiAgICAgICAgICAgIGlmIFsgLW4gIiR7TFRTX0FMSUFTfSIgXTsgdGhlbgog
ICAgICAgICAgICAgICAgbnZtX2VjaG8gIiR7TFRTX0FMSUFTfSI7CiAgICAgICAgICAgIGZpCiAg
ICAgICAgfSAmCiAgICBkb25lOwogICAgd2FpdCApIHwgY29tbWFuZCBzb3J0OwogICAgcmV0dXJu
Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xzICgpIAp7IAogICAgbG9jYWwgUEFUVEVSTjsKICAgIFBBVFRFUk49IiR7MS19IjsKICAg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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xzX2N1cnJlbnQgKCkgCnsgCiAgICBsb2NhbCBOVk1fTFNfQ1VSUkVOVF9OT0RFX1BBVEg7
CiAgICBpZiAhIE5WTV9MU19DVVJSRU5UX05PREVfUEFUSD0iJChjb21tYW5kIHdoaWNoIG5vZGUg
Mj4gL2Rldi9udWxsKSI7IHRoZW4KICAgICAgICBudm1fZWNobyAnbm9uZSc7CiAgICBlbHNlCiAg
ICAgICAgaWYgbnZtX3RyZWVfY29udGFpbnNfcGF0aCAiJChudm1fdmVyc2lvbl9kaXIgaW9qcyki
ICIke05WTV9MU19DVVJSRU5UX05PREVfUEFUSH0iOyB0aGVuCiAgICAgICAgICAgIG52bV9hZGRf
aW9qc19wcmVmaXggIiQoaW9qcyAtLXZlcnNpb24gMj4gL2Rldi9udWxsKSI7CiAgICAgICAgZWxz
ZQogICAgICAgICAgICBpZiBudm1fdHJlZV9jb250YWluc19wYXRoICIke05WTV9ESVJ9IiAiJHtO
Vk1fTFNfQ1VSUkVOVF9OT0RFX1BBVEh9IjsgdGhlbgogICAgICAgICAgICAgICAgbG9jYWwgVkVS
U0lPTjsKICAgICAgICAgICAgICAgIFZFUlNJT049IiQobm9kZSAtLXZlcnNpb24gMj4gL2Rldi9u
dWxsKSI7CiAgICAgICAgICAgICAgICBpZiBbICIke1ZFUlNJT059IiA9ICJ2MC42LjIxLXByZSIg
XTsgdGhlbgogICAgICAgICAgICAgICAgICAgIG52bV9lY2hvICd2MC42LjIxJzsKICAgICAgICAg
ICAgICAgIGVsc2UKICAgICAgICAgICAgICAgICAgICBudm1fZWNobyAiJHtWRVJTSU9OOi1ub25l
fSI7CiAgICAgICAgICAgICAgICBmaTsKICAgICAgICAgICAgZWxzZQogICAgICAgICAgICAgICAg
bnZtX2VjaG8gJ3N5c3RlbSc7CiAgICAgICAgICAgIGZpOwogICAgICAgIGZpOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xzX3JlbW90ZSAoKSAKeyAKICAgIGxvY2FsIFBBVFRFUk47CiAgICBQQVRURVJOPSIkezEt
fSI7CiAgICBpZiBudm1fdmFsaWRhdGVfaW1wbGljaXRfYWxpYXMgIiR7UEFUVEVSTn0iIDI+IC9k
ZXYvbnVsbDsgdGhlbgogICAgICAgIGxvY2FsIElNUExJQ0lUOwogICAgICAgIElNUExJQ0lUPSIk
KG52bV9wcmludF9pbXBsaWNpdF9hbGlhcyByZW1vdGUgIiR7UEFUVEVSTn0iKSI7CiAgICAgICAg
aWYgWyAteiAiJHtJTVBMSUNJVC19IiBdIHx8IFsgIiR7SU1QTElDSVR9IiA9ICdOL0EnIF07IHRo
ZW4KICAgICAgICAgICAgbnZtX2VjaG8gIk4vQSI7CiAgICAgICAgICAgIHJldHVybiAzOwogICAg
ICAgIGZpOwogICAgICAgIFBBVFRFUk49IiQoTlZNX0xUUz0iJHtOVk1fTFRTLX0iIG52bV9sc19y
ZW1vdGUgIiR7SU1QTElDSVR9IiB8IGNvbW1hbmQgdGFpbCAtMSB8IGNvbW1hbmQgYXdrICd7IHBy
aW50ICQxIH0nKSI7CiAgICBlbHNlCiAgICAgICAgaWYgWyAtbiAiJHtQQVRURVJOfSIgXTsgdGhl
bgogICAgICAgICAgICBQQVRURVJOPSIkKG52bV9lbnN1cmVfdmVyc2lvbl9wcmVmaXggIiR7UEFU
VEVSTn0iKSI7CiAgICAgICAgZWxzZQogICAgICAgICAgICBQQVRURVJOPSIuKiI7CiAgICAgICAg
Zmk7CiAgICBmaTsKICAgIE5WTV9MVFM9IiR7TlZNX0xUUy19IiBudm1fbHNfcmVtb3RlX2luZGV4
X3RhYiBub2RlIHN0ZCAiJHtQQVRURVJOfSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xzX3JlbW90ZV9pbmRleF90YWIgKCkgCnsgCiAgICBsb2NhbCBMVFM7CiAgICBMVFM9IiR7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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX2xzX3JlbW90ZV9pb2pzICgpIAp7IAogICAgTlZNX0xUUz0iJHtOVk1fTFRTLX0iIG52bV9s
c19yZW1vdGVfaW5kZXhfdGFiIGlvanMgc3RkICIkezEtfSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX21ha2VfYWxpYXMgKCkgCnsgCiAgICBsb2NhbCBBTElBUzsKICAgIEFMSUFTPSIkezEtfSI7
CiAgICBpZiBbIC16ICIke0FMSUFTfSIgXTsgdGhlbgogICAgICAgIG52bV9lcnIgImFuIGFsaWFz
IG5hbWUgaXMgcmVxdWlyZWQiOwogICAgICAgIHJldHVybiAxOwogICAgZmk7CiAgICBsb2NhbCBW
RVJTSU9OOwogICAgVkVSU0lPTj0iJHsyLX0iOwogICAgaWYgWyAteiAiJHtWRVJTSU9OfSIgXTsg
dGhlbgogICAgICAgIG52bV9lcnIgImFuIGFsaWFzIHRhcmdldCB2ZXJzaW9uIGlzIHJlcXVpcmVk
IjsKICAgICAgICByZXR1cm4gMjsKICAgIGZpOwogICAgbnZtX2VjaG8gIiR7VkVSU0lPTn0iIHwg
dGVlICIkKG52bV9hbGlhc19wYXRoKS8ke0FMSUFTfSIgPiAvZGV2L251bGwKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX21hdGNoX3ZlcnNpb24gKCkgCnsgCiAgICBsb2NhbCBOVk1fSU9KU19QUkVGSVg7CiAgICBO
Vk1fSU9KU19QUkVGSVg9IiQobnZtX2lvanNfcHJlZml4KSI7CiAgICBsb2NhbCBQUk9WSURFRF9W
RVJTSU9OOwogICAgUFJPVklERURfVkVSU0lPTj0iJDEiOwogICAgY2FzZSAiXyR7UFJPVklERURf
VkVSU0lPTn0iIGluIAogICAgICAgICJfJHtOVk1fSU9KU19QUkVGSVh9IiB8ICdfaW8uanMnKQog
ICAgICAgICAgICBudm1fdmVyc2lvbiAiJHtOVk1fSU9KU19QUkVGSVh9IgogICAgICAgIDs7CiAg
ICAgICAgJ19zeXN0ZW0nKQogICAgICAgICAgICBudm1fZWNobyAnc3lzdGVtJwogICAgICAgIDs7
CiAgICAgICAgKikKICAgICAgICAgICAgbnZtX3ZlcnNpb24gIiR7UFJPVklERURfVkVSU0lPTn0i
CiAgICAgICAgOzsKICAgIGVzYWMKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25vZGVfcHJlZml4ICgpIAp7IAogICAgbnZtX2VjaG8gJ25vZGUnCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25vZGVfdmVyc2lvbl9oYXNfc29sYXJpc19iaW5hcnkgKCkgCnsgCiAgICBsb2NhbCBOT0RF
X1ZFUlNJT047CiAgICBOT0RFX1ZFUlNJT049IiQxIjsKICAgIGxvY2FsIFNUUklQUEVEX0lPSlNf
VkVSU0lPTjsKICAgIFNUUklQUEVEX0lPSlNfVkVSU0lPTj0iJChudm1fc3RyaXBfaW9qc19wcmVm
aXggIiR7Tk9ERV9WRVJTSU9OfSIpIjsKICAgIGlmIFsgIl8ke1NUUklQUEVEX0lPSlNfVkVSU0lP
Tn0iICE9ICJfJHtOT0RFX1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAgcmV0dXJuIDE7CiAgICBm
aTsKICAgIG52bV92ZXJzaW9uX2dyZWF0ZXJfdGhhbl9vcl9lcXVhbF90byAiJHtOT0RFX1ZFUlNJ
T059IiB2MC44LjYgJiYgISBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7
Tk9ERV9WRVJTSU9OfSIgdjEuMC4wCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25vcm1hbGl6ZV9sdHMgKCkgCnsgCiAgICBsb2NhbCBMVFM7CiAgICBMVFM9IiR7MS19IjsK
ICAgIGNhc2UgIiR7TFRTfSIgaW4gCiAgICAgICAgbHRzLy1bMTIzNDU2Nzg5XSB8IGx0cy8tWzEy
MzQ1Njc4OV1bMDEyMzQ1Njc4OV0qKQogICAgICAgICAgICBsb2NhbCBOOwogICAgICAgICAgICBO
PSIkKGVjaG8gIiR7TFRTfSIgfCBjdXQgLWQgJy0nIC1mIDIpIjsKICAgICAgICAgICAgTj0kKChO
KzEpKTsKICAgICAgICAgICAgaWYgWyAkPyAtbmUgMCBdOyB0aGVuCiAgICAgICAgICAgICAgICBu
dm1fZWNobyAiJHtMVFN9IjsKICAgICAgICAgICAgICAgIHJldHVybiAwOwogICAgICAgICAgICBm
aTsKICAgICAgICAgICAgbG9jYWwgTlZNX0FMSUFTX0RJUjsKICAgICAgICAgICAgTlZNX0FMSUFT
X0RJUj0iJChudm1fYWxpYXNfcGF0aCkiOwogICAgICAgICAgICBsb2NhbCBSRVNVTFQ7CiAgICAg
ICAgICAgIFJFU1VMVD0iJChjb21tYW5kIGxzICIke05WTV9BTElBU19ESVJ9L2x0cyIgfCBjb21t
YW5kIHRhaWwgLW4gIiR7Tn0iIHwgY29tbWFuZCBoZWFkIC1uIDEpIjsKICAgICAgICAgICAgaWYg
WyAiJHtSRVNVTFR9IiAhPSAnKicgXTsgdGhlbgogICAgICAgICAgICAgICAgbnZtX2VjaG8gImx0
cy8ke1JFU1VMVH0iOwogICAgICAgICAgICBlbHNlCiAgICAgICAgICAgICAgICBudm1fZXJyICdU
aGF0IG1hbnkgTFRTIHJlbGVhc2VzIGRvIG5vdCBleGlzdCB5ZXQuJzsKICAgICAgICAgICAgICAg
IHJldHVybiAyOwogICAgICAgICAgICBmaQogICAgICAgIDs7CiAgICAgICAgKikKICAgICAgICAg
ICAgaWYgWyAiJHtMVFN9IiAhPSAiJChlY2hvICIke0xUU30iIHwgY29tbWFuZCB0ciAnWzp1cHBl
cjpdJyAnWzpsb3dlcjpdJykiIF07IHRoZW4KICAgICAgICAgICAgICAgIG52bV9lcnIgJ0xUUyBu
YW1lcyBtdXN0IGJlIGxvd2VyY2FzZSc7CiAgICAgICAgICAgICAgICByZXR1cm4gMzsKICAgICAg
ICAgICAgZmk7CiAgICAgICAgICAgIG52bV9lY2hvICIke0xUU30iCiAgICAgICAgOzsKICAgIGVz
YWMKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25vcm1hbGl6ZV92ZXJzaW9uICgpIAp7IAogICAgY29tbWFuZCBhd2sgJ0JFR0lOIHsKICAg
IHNwbGl0KEFSR1ZbMV0sIGEsIC9cLi8pOwogICAgcHJpbnRmICIlZCUwNmQlMDZkXG4iLCBhWzFd
LCBhWzJdLCBhWzNdOwogICAgZXhpdDsKICB9JyAiJHsxI3Z9Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25wbV9nbG9iYWxfbW9kdWxlcyAoKSAKeyAKICAgIGxvY2FsIE5QTUxJU1Q7CiAgICBsb2Nh
bCBWRVJTSU9OOwogICAgVkVSU0lPTj0iJDEiOwogICAgTlBNTElTVD0kKG52bSB1c2UgIiR7VkVS
U0lPTn0iID4gL2Rldi9udWxsICYmIG5wbSBsaXN0IC1nIC0tZGVwdGg9MCAyPiAvZGV2L251bGwg
fCBjb21tYW5kIHNlZCAtZSAnMWQnIC1lICcvVU5NRVQgUEVFUiBERVBFTkRFTkNZL2QnKTsKICAg
IGxvY2FsIElOU1RBTExTOwogICAgSU5TVEFMTFM9JChudm1fZWNobyAiJHtOUE1MSVNUfSIgfCBj
b21tYW5kIHNlZCAtZSAnLyAtPiAvIGQnIC1lICcvXChlbXB0eVwpLyBkJyAtZSAncy9eLiogXCgu
KkBbXiBdKlwpLiovXDEvJyAtZSAnL15ucG1AW14gXSouKiQvIGQnIC1lICcvXmNvcmVwYWNrQFte
IF0qLiokLyBkJyB8IGNvbW1hbmQgeGFyZ3MpOwogICAgbG9jYWwgTElOS1M7CiAgICBMSU5LUz0i
JChudm1fZWNobyAiJHtOUE1MSVNUfSIgfCBjb21tYW5kIHNlZCAtbiAncy8uKiAtPiBcKC4qXCkv
XDEvIHAnKSI7CiAgICBudm1fZWNobyAiJHtJTlNUQUxMU30gLy8vLyAke0xJTktTfSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX25wbXJjX2JhZF9uZXdzX2JlYXJzICgpIAp7IAogICAgbG9jYWwgTlZNX05QTVJDOwogICAg
TlZNX05QTVJDPSIkezEtfSI7CiAgICBpZiBbIC1uICIke05WTV9OUE1SQ30iIF0gJiYgWyAtZiAi
JHtOVk1fTlBNUkN9IiBdICYmIG52bV9ncmVwIC1FZSAnXihwcmVmaXh8Z2xvYmFsY29uZmlnKSAq
PScgPCAiJHtOVk1fTlBNUkN9IiA+IC9kZXYvbnVsbDsgdGhlbgogICAgICAgIHJldHVybiAwOwog
ICAgZmk7CiAgICByZXR1cm4gMQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX251bV92ZXJzaW9uX2dyb3VwcyAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT047CiAgICBWRVJT
SU9OPSIkezEtfSI7CiAgICBWRVJTSU9OPSIke1ZFUlNJT04jdn0iOwogICAgVkVSU0lPTj0iJHtW
RVJTSU9OJS59IjsKICAgIGlmIFsgLXogIiR7VkVSU0lPTn0iIF07IHRoZW4KICAgICAgICBudm1f
ZWNobyAiMCI7CiAgICAgICAgcmV0dXJuOwogICAgZmk7CiAgICBsb2NhbCBOVk1fTlVNX0RPVFM7
CiAgICBOVk1fTlVNX0RPVFM9JChudm1fZWNobyAiJHtWRVJTSU9OfSIgfCBjb21tYW5kIHNlZCAt
ZSAncy9bXlwuXS8vZycpOwogICAgbG9jYWwgTlZNX05VTV9HUk9VUFM7CiAgICBOVk1fTlVNX0dS
T1VQUz0iLiR7TlZNX05VTV9ET1RTfSI7CiAgICBudm1fZWNobyAiJHsjTlZNX05VTV9HUk9VUFN9
Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX252bXJjX2ludmFsaWRfbXNnICgpIAp7IAogICAgbG9jYWwgZXJyb3JfdGV4dDsKICAgIGVy
cm9yX3RleHQ9ImludmFsaWQgLm52bXJjIQphbGwgbm9uLWNvbW1lbnRlZCBjb250ZW50IChhbnl0
aGluZyBhZnRlciAjIGlzIGEgY29tbWVudCkgbXVzdCBiZSBlaXRoZXI6CiAgLSBhIHNpbmdsZSBi
YXJlIG52bS1yZWNvZ25pemVkIHZlcnNpb24taXNoCiAgLSBvciwgbXVsdGlwbGUgZGlzdGluY3Qg
a2V5LXZhbHVlIHBhaXJzLCBlYWNoIGtleS92YWx1ZSBzZXBhcmF0ZWQgYnkgYSBzaW5nbGUgZXF1
YWxzIHNpZ24gKD0pCgphZGRpdGlvbmFsbHksIGEgc2luZ2xlIGJhcmUgbnZtLXJlY29nbml6ZWQg
dmVyc2lvbi1pc2ggbXVzdCBiZSBwcmVzZW50IChhZnRlciBzdHJpcHBpbmcgY29tbWVudHMpLiI7
CiAgICBsb2NhbCB3YXJuX3RleHQ7CiAgICB3YXJuX3RleHQ9Im5vbi1jb21tZW50ZWQgY29udGVu
dCBwYXJzZWQ6CiR7MX0iOwogICAgbnZtX2VyciAiJChudm1fd3JhcF93aXRoX2NvbG9yX2NvZGUg
J3InICIke2Vycm9yX3RleHR9IikKCiQobnZtX3dyYXBfd2l0aF9jb2xvcl9jb2RlICd5JyAiJHt3
YXJuX3RleHR9IikiCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X2FsaWFzX3BhdGggKCkgCnsgCiAgICBsb2NhbCBOVk1fQUxJQVNfRElSOwogICAg
TlZNX0FMSUFTX0RJUj0iJHsxLX0iOwogICAgaWYgWyAteiAiJHtOVk1fQUxJQVNfRElSfSIgXTsg
dGhlbgogICAgICAgIG52bV9lcnIgJ0FuIGFsaWFzIGRpciBpcyByZXF1aXJlZC4nOwogICAgICAg
IHJldHVybiAxOwogICAgZmk7CiAgICBsb2NhbCBBTElBU19QQVRIOwogICAgQUxJQVNfUEFUSD0i
JHsyLX0iOwogICAgaWYgWyAteiAiJHtBTElBU19QQVRIfSIgXTsgdGhlbgogICAgICAgIG52bV9l
cnIgJ0FuIGFsaWFzIHBhdGggaXMgcmVxdWlyZWQuJzsKICAgICAgICByZXR1cm4gMjsKICAgIGZp
OwogICAgbG9jYWwgQUxJQVM7CiAgICBBTElBUz0iJHtBTElBU19QQVRIIyMiJHtOVk1fQUxJQVNf
RElSfSJcL30iOwogICAgbG9jYWwgREVTVDsKICAgIERFU1Q9IiQobnZtX2FsaWFzICIke0FMSUFT
fSIgMj4gL2Rldi9udWxsKSIgfHwgOjsKICAgIGlmIFsgLW4gIiR7REVTVH0iIF07IHRoZW4KICAg
ICAgICBOVk1fTk9fQ09MT1JTPSIke05WTV9OT19DT0xPUlMtfSIgTlZNX0xUUz0iJHtOVk1fTFRT
LX0iIERFRkFVTFQ9ZmFsc2UgbnZtX3ByaW50X2Zvcm1hdHRlZF9hbGlhcyAiJHtBTElBU30iICIk
e0RFU1R9IjsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X2NvbG9yX2NvZGUgKCkgCnsgCiAgICBjYXNlICIkezEtfSIgaW4gCiAgICAgICAg
JzAnKQogICAgICAgICAgICByZXR1cm4gMAogICAgICAgIDs7CiAgICAgICAgJ3InKQogICAgICAg
ICAgICBudm1fZWNobyAnMDszMW0nCiAgICAgICAgOzsKICAgICAgICAnUicpCiAgICAgICAgICAg
IG52bV9lY2hvICcxOzMxbScKICAgICAgICA7OwogICAgICAgICdnJykKICAgICAgICAgICAgbnZt
X2VjaG8gJzA7MzJtJwogICAgICAgIDs7CiAgICAgICAgJ0cnKQogICAgICAgICAgICBudm1fZWNo
byAnMTszMm0nCiAgICAgICAgOzsKICAgICAgICAnYicpCiAgICAgICAgICAgIG52bV9lY2hvICcw
OzM0bScKICAgICAgICA7OwogICAgICAgICdCJykKICAgICAgICAgICAgbnZtX2VjaG8gJzE7MzRt
JwogICAgICAgIDs7CiAgICAgICAgJ2MnKQogICAgICAgICAgICBudm1fZWNobyAnMDszNm0nCiAg
ICAgICAgOzsKICAgICAgICAnQycpCiAgICAgICAgICAgIG52bV9lY2hvICcxOzM2bScKICAgICAg
ICA7OwogICAgICAgICdtJykKICAgICAgICAgICAgbnZtX2VjaG8gJzA7MzVtJwogICAgICAgIDs7
CiAgICAgICAgJ00nKQogICAgICAgICAgICBudm1fZWNobyAnMTszNW0nCiAgICAgICAgOzsKICAg
ICAgICAneScpCiAgICAgICAgICAgIG52bV9lY2hvICcwOzMzbScKICAgICAgICA7OwogICAgICAg
ICdZJykKICAgICAgICAgICAgbnZtX2VjaG8gJzE7MzNtJwogICAgICAgIDs7CiAgICAgICAgJ2sn
KQogICAgICAgICAgICBudm1fZWNobyAnMDszMG0nCiAgICAgICAgOzsKICAgICAgICAnSycpCiAg
ICAgICAgICAgIG52bV9lY2hvICcxOzMwbScKICAgICAgICA7OwogICAgICAgICdlJykKICAgICAg
ICAgICAgbnZtX2VjaG8gJzA7MzdtJwogICAgICAgIDs7CiAgICAgICAgJ1cnKQogICAgICAgICAg
ICBudm1fZWNobyAnMTszN20nCiAgICAgICAgOzsKICAgICAgICAqKQogICAgICAgICAgICBudm1f
ZXJyICJJbnZhbGlkIGNvbG9yIGNvZGU6ICR7MS19IjsKICAgICAgICAgICAgcmV0dXJuIDEKICAg
ICAgICA7OwogICAgZXNhYwp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X2RlZmF1bHRfYWxpYXMgKCkgCnsgCiAgICBsb2NhbCBBTElBUzsKICAgIEFMSUFT
PSIkezEtfSI7CiAgICBpZiBbIC16ICIke0FMSUFTfSIgXTsgdGhlbgogICAgICAgIG52bV9lcnIg
J0EgZGVmYXVsdCBhbGlhcyBpcyByZXF1aXJlZC4nOwogICAgICAgIHJldHVybiAxOwogICAgZmk7
CiAgICBsb2NhbCBERVNUOwogICAgREVTVD0iJChudm1fcHJpbnRfaW1wbGljaXRfYWxpYXMgbG9j
YWwgIiR7QUxJQVN9IikiOwogICAgaWYgWyAtbiAiJHtERVNUfSIgXTsgdGhlbgogICAgICAgIE5W
TV9OT19DT0xPUlM9IiR7TlZNX05PX0NPTE9SUy19IiBERUZBVUxUPXRydWUgbnZtX3ByaW50X2Zv
cm1hdHRlZF9hbGlhcyAiJHtBTElBU30iICIke0RFU1R9IjsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X2Zvcm1hdHRlZF9hbGlhcyAoKSAKeyAKICAgIGxvY2FsIEFMSUFTOwogICAgQUxJ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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X2ltcGxpY2l0X2FsaWFzICgpIAp7IAogICAgaWYgWyAiXyQxIiAhPSAiX2xvY2Fs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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X25wbV92ZXJzaW9uICgpIAp7IAogICAgaWYgbnZtX2hhcyAibnBtIjsgdGhlbgog
ICAgICAgIGxvY2FsIE5QTV9WRVJTSU9OOwogICAgICAgIE5QTV9WRVJTSU9OPSIkKG5wbSAtLXZl
cnNpb24gMj4gL2Rldi9udWxsKSI7CiAgICAgICAgaWYgWyAtbiAiJHtOUE1fVkVSU0lPTn0iIF07
IHRoZW4KICAgICAgICAgICAgY29tbWFuZCBwcmludGYgIiAobnBtIHYke05QTV9WRVJTSU9OfSki
OwogICAgICAgIGZpOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ByaW50X3ZlcnNpb25zICgpIAp7IAogICAgbG9jYWwgTlZNX0NVUlJFTlQ7CiAgICBOVk1f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==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Byb2Nlc3NfbnZtcmMgKCkgCnsgCiAgICBsb2NhbCBOVk1SQ19QQVRIOwogICAgTlZNUkNf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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Byb2Nlc3NfcGFyYW1ldGVycyAoKSAKeyAKICAgIGxvY2FsIE5WTV9BVVRPX01PREU7CiAg
ICBOVk1fQVVUT19NT0RFPSd1c2UnOwogICAgd2hpbGUgWyAiJCMiIC1uZSAwIF07IGRvCiAgICAg
ICAgY2FzZSAiJDEiIGluIAogICAgICAgICAgICAtLWluc3RhbGwpCiAgICAgICAgICAgICAgICBO
Vk1fQVVUT19NT0RFPSdpbnN0YWxsJwogICAgICAgICAgICA7OwogICAgICAgICAgICAtLW5vLXVz
ZSkKICAgICAgICAgICAgICAgIE5WTV9BVVRPX01PREU9J25vbmUnCiAgICAgICAgICAgIDs7CiAg
ICAgICAgZXNhYzsKICAgICAgICBzaGlmdDsKICAgIGRvbmU7CiAgICBudm1fYXV0byAiJHtOVk1f
QVVUT19NT0RFfSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3JjX3ZlcnNpb24gKCkgCnsgCiAgICBleHBvcnQgTlZNX1JDX1ZFUlNJT049Jyc7CiAgICBs
b2NhbCBOVk1SQ19QQVRIOwogICAgTlZNUkNfUEFUSD0iJChudm1fZmluZF9udm1yYykiOwogICAg
aWYgWyAhIC1lICIke05WTVJDX1BBVEh9IiBdOyB0aGVuCiAgICAgICAgaWYgWyAiJHtOVk1fU0lM
RU5UOi0wfSIgLW5lIDEgXTsgdGhlbgogICAgICAgICAgICBudm1fZXJyICJObyAubnZtcmMgZmls
ZSBmb3VuZCI7CiAgICAgICAgZmk7CiAgICAgICAgcmV0dXJuIDE7CiAgICBmaTsKICAgIGlmICEg
TlZNX1JDX1ZFUlNJT049IiQobnZtX3Byb2Nlc3NfbnZtcmMgIiR7TlZNUkNfUEFUSH0iKSI7IHRo
ZW4KICAgICAgICByZXR1cm4gMTsKICAgIGZpOwogICAgaWYgWyAteiAiJHtOVk1fUkNfVkVSU0lP
Tn0iIF07IHRoZW4KICAgICAgICBpZiBbICIke05WTV9TSUxFTlQ6LTB9IiAtbmUgMSBdOyB0aGVu
CiAgICAgICAgICAgIG52bV9lcnIgIldhcm5pbmc6IGVtcHR5IC5udm1yYyBmaWxlIGZvdW5kIGF0
IFwiJHtOVk1SQ19QQVRIfVwiIjsKICAgICAgICBmaTsKICAgICAgICByZXR1cm4gMjsKICAgIGZp
OwogICAgaWYgWyAiJHtOVk1fU0lMRU5UOi0wfSIgLW5lIDEgXTsgdGhlbgogICAgICAgIG52bV9l
Y2hvICJGb3VuZCAnJHtOVk1SQ19QQVRIfScgd2l0aCB2ZXJzaW9uIDwke05WTV9SQ19WRVJTSU9O
fT4iOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3JlbW90ZV92ZXJzaW9uICgpIAp7IAogICAgbG9jYWwgUEFUVEVSTjsKICAgIFBBVFRFUk49
IiR7MS19IjsKICAgIGxvY2FsIFZFUlNJT047CiAgICBpZiBudm1fdmFsaWRhdGVfaW1wbGljaXRf
YWxpYXMgIiR7UEFUVEVSTn0iIDI+IC9kZXYvbnVsbDsgdGhlbgogICAgICAgIGNhc2UgIiR7UEFU
VEVSTn0iIGluIAogICAgICAgICAgICAiJChudm1faW9qc19wcmVmaXgpIikKICAgICAgICAgICAg
ICAgIFZFUlNJT049IiQoTlZNX0xUUz0iJHtOVk1fTFRTLX0iIG52bV9sc19yZW1vdGVfaW9qcyB8
IGNvbW1hbmQgdGFpbCAtMSkiICYmIDoKICAgICAgICAgICAgOzsKICAgICAgICAgICAgKikKICAg
ICAgICAgICAgICAgIFZFUlNJT049IiQoTlZNX0xUUz0iJHtOVk1fTFRTLX0iIG52bV9sc19yZW1v
dGUgIiR7UEFUVEVSTn0iKSIgJiYgOgogICAgICAgICAgICA7OwogICAgICAgIGVzYWM7CiAgICBl
bHNlCiAgICAgICAgVkVSU0lPTj0iJChOVk1fTFRTPSIke05WTV9MVFMtfSIgbnZtX3JlbW90ZV92
ZXJzaW9ucyAiJHtQQVRURVJOfSIgfCBjb21tYW5kIHRhaWwgLTEpIjsKICAgIGZpOwogICAgaWYg
WyAtbiAiJHtOVk1fVkVSU0lPTl9PTkxZLX0iIF07IHRoZW4KICAgICAgICBjb21tYW5kIGF3ayAn
QkVHSU4gewogICAgICBuID0gc3BsaXQoQVJHVlsxXSwgYSk7CiAgICAgIHByaW50IGFbMV0KICAg
IH0nICIke1ZFUlNJT059IjsKICAgIGVsc2UKICAgICAgICBudm1fZWNobyAiJHtWRVJTSU9OfSI7
CiAgICBmaTsKICAgIGlmIFsgIiR7VkVSU0lPTn0iID0gJ04vQScgXTsgdGhlbgogICAgICAgIHJl
dHVybiAzOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3JlbW90ZV92ZXJzaW9ucyAoKSAKeyAKICAgIGxvY2FsIE5WTV9JT0pTX1BSRUZJWDsKICAg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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Jlc29sdmVfYWxpYXMgKCkgCnsgCiAgICBpZiBbIC16ICIkezEtfSIgXTsgdGhlbgogICAg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' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Jlc29sdmVfbG9jYWxfYWxpYXMgKCkgCnsgCiAgICBpZiBbIC16ICIkezEtfSIgXTsgdGhl
bgogICAgICAgIHJldHVybiAxOwogICAgZmk7CiAgICBsb2NhbCBWRVJTSU9OOwogICAgbG9jYWwg
RVhJVF9DT0RFOwogICAgVkVSU0lPTj0iJChudm1fcmVzb2x2ZV9hbGlhcyAiJHsxLX0iKSI7CiAg
ICBFWElUX0NPREU9JD87CiAgICBpZiBbIC16ICIke1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAg
cmV0dXJuICRFWElUX0NPREU7CiAgICBmaTsKICAgIGlmIFsgIl8ke1ZFUlNJT059IiAhPSAnX+KI
nicgXTsgdGhlbgogICAgICAgIG52bV92ZXJzaW9uICIke1ZFUlNJT059IjsKICAgIGVsc2UKICAg
ICAgICBudm1fZWNobyAiJHtWRVJTSU9OfSI7CiAgICBmaQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Nhbml0aXplX2F1dGhfaGVhZGVyICgpIAp7IAogICAgbnZtX2VjaG8gIiQxIiB8IGNvbW1h
bmQgc2VkICdzL1teYS16QS1aMC05OjtfLiAtXS8vZycKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3Nhbml0aXplX3BhdGggKCkgCnsgCiAgICBsb2NhbCBTQU5JVElaRURfUEFUSDsKICAgIFNB
TklUSVpFRF9QQVRIPSIkezEtfSI7CiAgICBpZiBbICJfJHtTQU5JVElaRURfUEFUSH0iICE9ICJf
JHtOVk1fRElSfSIgXTsgdGhlbgogICAgICAgIFNBTklUSVpFRF9QQVRIPSIkKG52bV9lY2hvICIk
e1NBTklUSVpFRF9QQVRIfSIgfCBjb21tYW5kIHNlZCAtZSAicyMke05WTV9ESVJ9I1wke05WTV9E
SVJ9I2ciKSI7CiAgICBmaTsKICAgIGlmIFsgIl8ke1NBTklUSVpFRF9QQVRIfSIgIT0gIl8ke0hP
TUV9IiBdOyB0aGVuCiAgICAgICAgU0FOSVRJWkVEX1BBVEg9IiQobnZtX2VjaG8gIiR7U0FOSVRJ
WkVEX1BBVEh9IiB8IGNvbW1hbmQgc2VkIC1lICJzIyR7SE9NRX0jXCR7SE9NRX0jZyIpIjsKICAg
IGZpOwogICAgbnZtX2VjaG8gIiR7U0FOSVRJWkVEX1BBVEh9Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3NldF9jb2xvcnMgKCkgCnsgCiAgICBpZiBbICIkeyMxfSIgLWVxIDUgXSAmJiBudm1fZWNo
byAiJDEiIHwgbnZtX2dyZXAgLUUgIl5bclJnR2JCY0N5WW1Na0tlV117MSx9JCIgPiAvZGV2L251
bGw7IHRoZW4KICAgICAgICBsb2NhbCBJTlNUQUxMRURfQ09MT1I7CiAgICAgICAgbG9jYWwgTFRT
X0FORF9TWVNURU1fQ09MT1I7CiAgICAgICAgbG9jYWwgQ1VSUkVOVF9DT0xPUjsKICAgICAgICBs
b2NhbCBOT1RfSU5TVEFMTEVEX0NPTE9SOwogICAgICAgIGxvY2FsIERFRkFVTFRfQ09MT1I7CiAg
ICAgICAgSU5TVEFMTEVEX0NPTE9SPSIkKGVjaG8gIiQxIiB8IGF3ayAneyBwcmludCBzdWJzdHIo
JDAsIDEsIDEpOyB9JykiOwogICAgICAgIExUU19BTkRfU1lTVEVNX0NPTE9SPSIkKGVjaG8gIiQx
IiB8IGF3ayAneyBwcmludCBzdWJzdHIoJDAsIDIsIDEpOyB9JykiOwogICAgICAgIENVUlJFTlRf
Q09MT1I9IiQoZWNobyAiJDEiIHwgYXdrICd7IHByaW50IHN1YnN0cigkMCwgMywgMSk7IH0nKSI7
CiAgICAgICAgTk9UX0lOU1RBTExFRF9DT0xPUj0iJChlY2hvICIkMSIgfCBhd2sgJ3sgcHJpbnQg
c3Vic3RyKCQwLCA0LCAxKTsgfScpIjsKICAgICAgICBERUZBVUxUX0NPTE9SPSIkKGVjaG8gIiQx
IiB8IGF3ayAneyBwcmludCBzdWJzdHIoJDAsIDUsIDEpOyB9JykiOwogICAgICAgIGlmICEgbnZt
X2hhc19jb2xvcnM7IHRoZW4KICAgICAgICAgICAgbnZtX2VjaG8gIlNldHRpbmcgY29sb3JzIHRv
OiAke0lOU1RBTExFRF9DT0xPUn0gJHtMVFNfQU5EX1NZU1RFTV9DT0xPUn0gJHtDVVJSRU5UX0NP
TE9SfSAke05PVF9JTlNUQUxMRURfQ09MT1J9ICR7REVGQVVMVF9DT0xPUn0iOwogICAgICAgICAg
ICBudm1fZWNobyAiV0FSTklORzogQ29sb3JzIG1heSBub3QgZGlzcGxheSBiZWNhdXNlIHRoZXkg
YXJlIG5vdCBzdXBwb3J0ZWQgaW4gdGhpcyBzaGVsbC4iOwogICAgICAgIGVsc2UKICAgICAgICAg
ICAgbnZtX2VjaG9fd2l0aF9jb2xvcnMgIlNldHRpbmcgY29sb3JzIHRvOiAkKG52bV93cmFwX3dp
dGhfY29sb3JfY29kZSAiJHtJTlNUQUxMRURfQ09MT1J9IiAiJHtJTlNUQUxMRURfQ09MT1J9Iikk
KG52bV93cmFwX3dpdGhfY29sb3JfY29kZSAiJHtMVFNfQU5EX1NZU1RFTV9DT0xPUn0iICIke0xU
U19BTkRfU1lTVEVNX0NPTE9SfSIpJChudm1fd3JhcF93aXRoX2NvbG9yX2NvZGUgIiR7Q1VSUkVO
VF9DT0xPUn0iICIke0NVUlJFTlRfQ09MT1J9IikkKG52bV93cmFwX3dpdGhfY29sb3JfY29kZSAi
JHtOT1RfSU5TVEFMTEVEX0NPTE9SfSIgIiR7Tk9UX0lOU1RBTExFRF9DT0xPUn0iKSQobnZtX3dy
YXBfd2l0aF9jb2xvcl9jb2RlICIke0RFRkFVTFRfQ09MT1J9IiAiJHtERUZBVUxUX0NPTE9SfSIp
IjsKICAgICAgICBmaTsKICAgICAgICBleHBvcnQgTlZNX0NPTE9SUz0iJDEiOwogICAgZWxzZQog
ICAgICAgIHJldHVybiAxNzsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3N0ZG91dF9pc190ZXJtaW5hbCAoKSAKeyAKICAgIFsgLXQgMSBdCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3N0cmlwX2lvanNfcHJlZml4ICgpIAp7IAogICAgbG9jYWwgTlZNX0lPSlNfUFJFRklYOwog
ICAgTlZNX0lPSlNfUFJFRklYPSIkKG52bV9pb2pzX3ByZWZpeCkiOwogICAgY2FzZSAiJHsxLX0i
IGluIAogICAgICAgICIke05WTV9JT0pTX1BSRUZJWH0iKQogICAgICAgICAgICBudm1fZWNobwog
ICAgICAgIDs7CiAgICAgICAgKikKICAgICAgICAgICAgbnZtX2VjaG8gIiR7MSMiJHtOVk1fSU9K
U19QUkVGSVh9Ii19IgogICAgICAgIDs7CiAgICBlc2FjCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3N0cmlwX3BhdGggKCkgCnsgCiAgICBpZiBbIC16ICIke05WTV9ESVItfSIgXTsgdGhlbgog
ICAgICAgIG52bV9lcnIgJyR7TlZNX0RJUn0gbm90IHNldCEnOwogICAgICAgIHJldHVybiAxOwog
ICAgZmk7CiAgICBjb21tYW5kIHByaW50ZiAlcyAiJHsxLX0iIHwgY29tbWFuZCBhd2sgLXYgTlZN
X0RJUj0iJHtOVk1fRElSfSIgLXYgUlM9OiAnCiAgaW5kZXgoJDAsIE5WTV9ESVIpID09IDEgewog
ICAgcGF0aCA9IHN1YnN0cigkMCwgbGVuZ3RoKE5WTV9ESVIpICsgMSkKICAgIGlmIChwYXRoIH4g
Il4oL3ZlcnNpb25zL1teL10qKT8vW14vXSonIiR7Mi19IicuKiQiKSB7IG5leHQgfQogIH0KICAj
IFRoZSBmaW5hbCBSVCB3aWxsIGNvbnRhaW4gYSBjb2xvbiBpZiB0aGUgaW5wdXQgaGFzIGEgdHJh
aWxpbmcgY29sb24sIG9yIGEgbnVsbCBzdHJpbmcgb3RoZXJ3aXNlCiAgeyBwcmludGYgIiVzJXMi
LCBzZXAsICQwOyBzZXA9UlMgfSBFTkQgeyBwcmludGYgIiVzIiwgUlQgfScKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3N1cHBvcnRzX3h6ICgpIAp7IAogICAgaWYgWyAteiAiJHsxLX0iIF07IHRoZW4KICAgICAg
ICByZXR1cm4gMTsKICAgIGZpOwogICAgbG9jYWwgTlZNX09TOwogICAgTlZNX09TPSIkKG52bV9n
ZXRfb3MpIjsKICAgIGlmIFsgIl8ke05WTV9PU30iID0gJ19kYXJ3aW4nIF07IHRoZW4KICAgICAg
ICBsb2NhbCBNQUNPU19WRVJTSU9OOwogICAgICAgIE1BQ09TX1ZFUlNJT049IiQoc3dfdmVycyAt
cHJvZHVjdFZlcnNpb24pIjsKICAgICAgICBpZiBudm1fdmVyc2lvbl9ncmVhdGVyICIxMC45LjAi
ICIke01BQ09TX1ZFUlNJT059IjsgdGhlbgogICAgICAgICAgICByZXR1cm4gMTsKICAgICAgICBm
aTsKICAgIGVsc2UKICAgICAgICBpZiBbICJfJHtOVk1fT1N9IiA9ICdfZnJlZWJzZCcgXTsgdGhl
bgogICAgICAgICAgICBpZiAhIFsgLWUgJy91c3IvbGliL2xpYmx6bWEuc28nIF07IHRoZW4KICAg
ICAgICAgICAgICAgIHJldHVybiAxOwogICAgICAgICAgICBmaTsKICAgICAgICBlbHNlCiAgICAg
ICAgICAgIGlmICEgY29tbWFuZCB3aGljaCB4eiA+IC9kZXYvbnVsbCAyPiYxOyB0aGVuCiAgICAg
ICAgICAgICAgICByZXR1cm4gMTsKICAgICAgICAgICAgZmk7CiAgICAgICAgZmk7CiAgICBmaTsK
ICAgIGlmIG52bV9pc19tZXJnZWRfbm9kZV92ZXJzaW9uICIkezF9IjsgdGhlbgogICAgICAgIHJl
dHVybiAwOwogICAgZmk7CiAgICBpZiBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxf
dG8gIiR7MX0iICIwLjEyLjEwIiAmJiBudm1fdmVyc2lvbl9ncmVhdGVyICIwLjEzLjAiICIkezF9
IjsgdGhlbgogICAgICAgIHJldHVybiAwOwogICAgZmk7CiAgICBpZiBudm1fdmVyc2lvbl9ncmVh
dGVyX3RoYW5fb3JfZXF1YWxfdG8gIiR7MX0iICIwLjEwLjQyIiAmJiBudm1fdmVyc2lvbl9ncmVh
dGVyICIwLjExLjAiICIkezF9IjsgdGhlbgogICAgICAgIHJldHVybiAwOwogICAgZmk7CiAgICBj
YXNlICIke05WTV9PU30iIGluIAogICAgICAgIGRhcndpbikKICAgICAgICAgICAgbnZtX3ZlcnNp
b25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICIkezF9IiAiMi4zLjIiCiAgICAgICAgOzsKICAg
ICAgICAqKQogICAgICAgICAgICBudm1fdmVyc2lvbl9ncmVhdGVyX3RoYW5fb3JfZXF1YWxfdG8g
IiR7MX0iICIxLjAuMCIKICAgICAgICA7OwogICAgZXNhYzsKICAgIHJldHVybiAkPwp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3RyZWVfY29udGFpbnNfcGF0aCAoKSAKeyAKICAgIGxvY2FsIHRyZWU7CiAgICB0cmVlPSIk
ezEtfSI7CiAgICBsb2NhbCBub2RlX3BhdGg7CiAgICBub2RlX3BhdGg9IiR7Mi19IjsKICAgIGlm
IFsgIkAke3RyZWV9QCIgPSAiQEAiIF0gfHwgWyAiQCR7bm9kZV9wYXRofUAiID0gIkBAIiBdOyB0
aGVuCiAgICAgICAgbnZtX2VyciAiYm90aCB0aGUgdHJlZSBhbmQgdGhlIG5vZGUgcGF0aCBhcmUg
cmVxdWlyZWQiOwogICAgICAgIHJldHVybiAyOwogICAgZmk7CiAgICBsb2NhbCBwcmV2aW91c19w
YXRoZGlyOwogICAgcHJldmlvdXNfcGF0aGRpcj0iJHtub2RlX3BhdGh9IjsKICAgIGxvY2FsIHBh
dGhkaXI7CiAgICBwYXRoZGlyPSQoZGlybmFtZSAiJHtwcmV2aW91c19wYXRoZGlyfSIpOwogICAg
d2hpbGUgWyAiJHtwYXRoZGlyfSIgIT0gJycgXSAmJiBbICIke3BhdGhkaXJ9IiAhPSAnLicgXSAm
JiBbICIke3BhdGhkaXJ9IiAhPSAnLycgXSAmJiBbICIke3BhdGhkaXJ9IiAhPSAiJHt0cmVlfSIg
XSAmJiBbICIke3BhdGhkaXJ9IiAhPSAiJHtwcmV2aW91c19wYXRoZGlyfSIgXTsgZG8KICAgICAg
ICBwcmV2aW91c19wYXRoZGlyPSIke3BhdGhkaXJ9IjsKICAgICAgICBwYXRoZGlyPSQoZGlybmFt
ZSAiJHtwcmV2aW91c19wYXRoZGlyfSIpOwogICAgZG9uZTsKICAgIFsgIiR7cGF0aGRpcn0iID0g
IiR7dHJlZX0iIF0KfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3VzZV9pZl9uZWVkZWQgKCkgCnsgCiAgICBpZiBbICJfJHsxLX0iID0gIl8kKG52bV9sc19j
dXJyZW50KSIgXTsgdGhlbgogICAgICAgIHJldHVybjsKICAgIGZpOwogICAgbnZtIHVzZSAiJEAi
Cn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZhbGlkYXRlX2ltcGxpY2l0X2FsaWFzICgpIAp7IAogICAgbG9jYWwgTlZNX0lPSlNfUFJF
RklYOwogICAgTlZNX0lPSlNfUFJFRklYPSIkKG52bV9pb2pzX3ByZWZpeCkiOwogICAgbG9jYWwg
TlZNX05PREVfUFJFRklYOwogICAgTlZNX05PREVfUFJFRklYPSIkKG52bV9ub2RlX3ByZWZpeCki
OwogICAgY2FzZSAiJDEiIGluIAogICAgICAgICJzdGFibGUiIHwgInVuc3RhYmxlIiB8ICIke05W
TV9JT0pTX1BSRUZJWH0iIHwgIiR7TlZNX05PREVfUFJFRklYfSIpCiAgICAgICAgICAgIHJldHVy
bgogICAgICAgIDs7CiAgICAgICAgKikKICAgICAgICAgICAgbnZtX2VyciAiT25seSBpbXBsaWNp
dCBhbGlhc2VzICdzdGFibGUnLCAndW5zdGFibGUnLCAnJHtOVk1fSU9KU19QUkVGSVh9JywgYW5k
ICcke05WTV9OT0RFX1BSRUZJWH0nIGFyZSBzdXBwb3J0ZWQuIjsKICAgICAgICAgICAgcmV0dXJu
IDEKICAgICAgICA7OwogICAgZXNhYwp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZlcnNpb24gKCkgCnsgCiAgICBsb2NhbCBQQVRURVJOOwogICAgUEFUVEVSTj0iJHsxLX0i
OwogICAgbG9jYWwgVkVSU0lPTjsKICAgIGlmIFsgLXogIiR7UEFUVEVSTn0iIF07IHRoZW4KICAg
ICAgICBQQVRURVJOPSdjdXJyZW50JzsKICAgIGZpOwogICAgaWYgWyAiJHtQQVRURVJOfSIgPSAi
Y3VycmVudCIgXTsgdGhlbgogICAgICAgIG52bV9sc19jdXJyZW50OwogICAgICAgIHJldHVybiAk
PzsKICAgIGZpOwogICAgbG9jYWwgTlZNX05PREVfUFJFRklYOwogICAgTlZNX05PREVfUFJFRklY
PSIkKG52bV9ub2RlX3ByZWZpeCkiOwogICAgY2FzZSAiXyR7UEFUVEVSTn0iIGluIAogICAgICAg
ICJfJHtOVk1fTk9ERV9QUkVGSVh9IiB8ICJfJHtOVk1fTk9ERV9QUkVGSVh9LSIpCiAgICAgICAg
ICAgIFBBVFRFUk49InN0YWJsZSIKICAgICAgICA7OwogICAgZXNhYzsKICAgIFZFUlNJT049IiQo
bnZtX2xzICIke1BBVFRFUk59IiB8IGNvbW1hbmQgdGFpbCAtMSkiOwogICAgaWYgWyAteiAiJHtW
RVJTSU9OfSIgXSB8fCBbICJfJHtWRVJTSU9OfSIgPSAiX04vQSIgXTsgdGhlbgogICAgICAgIG52
bV9lY2hvICJOL0EiOwogICAgICAgIHJldHVybiAzOwogICAgZmk7CiAgICBudm1fZWNobyAiJHtW
RVJTSU9OfSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZlcnNpb25fZGlyICgpIAp7IAogICAgbG9jYWwgTlZNX1dISUNIX0RJUjsKICAgIE5WTV9X
SElDSF9ESVI9IiR7MS19IjsKICAgIGlmIFsgLXogIiR7TlZNX1dISUNIX0RJUn0iIF0gfHwgWyAi
JHtOVk1fV0hJQ0hfRElSfSIgPSAibmV3IiBdOyB0aGVuCiAgICAgICAgbnZtX2VjaG8gIiR7TlZN
X0RJUn0vdmVyc2lvbnMvbm9kZSI7CiAgICBlbHNlCiAgICAgICAgaWYgWyAiXyR7TlZNX1dISUNI
X0RJUn0iID0gIl9pb2pzIiBdOyB0aGVuCiAgICAgICAgICAgIG52bV9lY2hvICIke05WTV9ESVJ9
L3ZlcnNpb25zL2lvLmpzIjsKICAgICAgICBlbHNlCiAgICAgICAgICAgIGlmIFsgIl8ke05WTV9X
SElDSF9ESVJ9IiA9ICJfb2xkIiBdOyB0aGVuCiAgICAgICAgICAgICAgICBudm1fZWNobyAiJHtO
Vk1fRElSfSI7CiAgICAgICAgICAgIGVsc2UKICAgICAgICAgICAgICAgIG52bV9lcnIgJ3Vua25v
d24gdmVyc2lvbiBkaXInOwogICAgICAgICAgICAgICAgcmV0dXJuIDM7CiAgICAgICAgICAgIGZp
OwogICAgICAgIGZpOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZlcnNpb25fZ3JlYXRlciAoKSAKeyAKICAgIGNvbW1hbmQgYXdrICdCRUdJTiB7CiAgICBp
ZiAoQVJHVlsxXSA9PSAiIiB8fCBBUkdWWzJdID09ICIiKSBleGl0KDEpCiAgICBzcGxpdChBUkdW
WzFdLCBhLCAvXC4vKTsKICAgIHNwbGl0KEFSR1ZbMl0sIGIsIC9cLi8pOwogICAgZm9yIChpPTE7
IGk8PTM7IGkrKykgewogICAgICBpZiAoYVtpXSAmJiBhW2ldICF+IC9eWzAtOV0rJC8pIGV4aXQo
Mik7CiAgICAgIGlmIChiW2ldICYmIGJbaV0gIX4gL15bMC05XSskLykgeyBleGl0KDApOyB9CiAg
ICAgIGlmIChhW2ldIDwgYltpXSkgZXhpdCgzKTsKICAgICAgZWxzZSBpZiAoYVtpXSA+IGJbaV0p
IGV4aXQoMCk7CiAgICB9CiAgICBleGl0KDQpCiAgfScgIiR7MSN2fSIgIiR7MiN2fSIKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZlcnNpb25fZ3JlYXRlcl90aGFuX29yX2VxdWFsX3RvICgpIAp7IAogICAgY29tbWFuZCBh
d2sgJ0JFR0lOIHsKICAgIGlmIChBUkdWWzFdID09ICIiIHx8IEFSR1ZbMl0gPT0gIiIpIGV4aXQo
MSkKICAgIHNwbGl0KEFSR1ZbMV0sIGEsIC9cLi8pOwogICAgc3BsaXQoQVJHVlsyXSwgYiwgL1wu
Lyk7CiAgICBmb3IgKGk9MTsgaTw9MzsgaSsrKSB7CiAgICAgIGlmIChhW2ldICYmIGFbaV0gIX4g
L15bMC05XSskLykgZXhpdCgyKTsKICAgICAgaWYgKGFbaV0gPCBiW2ldKSBleGl0KDMpOwogICAg
ICBlbHNlIGlmIChhW2ldID4gYltpXSkgZXhpdCgwKTsKICAgIH0KICAgIGV4aXQoMCkKICB9JyAi
JHsxI3Z9IiAiJHsyI3Z9Igp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3ZlcnNpb25fcGF0aCAoKSAKeyAKICAgIGxvY2FsIFZFUlNJT047CiAgICBWRVJTSU9OPSIk
ezEtfSI7CiAgICBpZiBbIC16ICIke1ZFUlNJT059IiBdOyB0aGVuCiAgICAgICAgbnZtX2VyciAn
dmVyc2lvbiBpcyByZXF1aXJlZCc7CiAgICAgICAgcmV0dXJuIDM7CiAgICBlbHNlCiAgICAgICAg
aWYgbnZtX2lzX2lvanNfdmVyc2lvbiAiJHtWRVJTSU9OfSI7IHRoZW4KICAgICAgICAgICAgbnZt
X2VjaG8gIiQobnZtX3ZlcnNpb25fZGlyIGlvanMpLyQobnZtX3N0cmlwX2lvanNfcHJlZml4ICIk
e1ZFUlNJT059IikiOwogICAgICAgIGVsc2UKICAgICAgICAgICAgaWYgbnZtX3ZlcnNpb25fZ3Jl
YXRlciAwLjEyLjAgIiR7VkVSU0lPTn0iOyB0aGVuCiAgICAgICAgICAgICAgICBudm1fZWNobyAi
JChudm1fdmVyc2lvbl9kaXIgb2xkKS8ke1ZFUlNJT059IjsKICAgICAgICAgICAgZWxzZQogICAg
ICAgICAgICAgICAgbnZtX2VjaG8gIiQobnZtX3ZlcnNpb25fZGlyIG5ldykvJHtWRVJTSU9OfSI7
CiAgICAgICAgICAgIGZpOwogICAgICAgIGZpOwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3dyYXBfd2l0aF9jb2xvcl9jb2RlICgpIAp7IAogICAgbG9jYWwgQ09ERTsKICAgIENPREU9
IiQobnZtX3ByaW50X2NvbG9yX2NvZGUgIiR7MX0iIDI+IC9kZXYvbnVsbCB8fCA6KSI7CiAgICBs
b2NhbCBURVhUOwogICAgVEVYVD0iJHsyLX0iOwogICAgaWYgbnZtX2hhc19jb2xvcnMgJiYgWyAt
biAiJHtDT0RFfSIgXTsgdGhlbgogICAgICAgIG52bV9lY2hvX3dpdGhfY29sb3JzICJcMDMzWyR7
Q09ERX0ke1RFWFR9XDAzM1swbSI7CiAgICBlbHNlCiAgICAgICAgbnZtX2VjaG8gIiR7VEVYVH0i
OwogICAgZmkKfQo=' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'bnZtX3dyaXRlX252bXJjICgpIAp7IAogICAgbG9jYWwgVkVSU0lPTl9TVFJJTkc7CiAgICBWRVJT
SU9OX1NUUklORz0kKG52bV92ZXJzaW9uICIkezEtfSIpOwogICAgaWYgWyAiJHtWRVJTSU9OX1NU
UklOR30iID0gJ+KInicgXSB8fCBbICIke1ZFUlNJT05fU1RSSU5HfSIgPSAnTi9BJyBdOyB0aGVu
CiAgICAgICAgcmV0dXJuIDE7CiAgICBmaTsKICAgIGVjaG8gIiR7VkVSU0lPTl9TVFJJTkd9IiB8
IHRlZSAiJFBXRCIvLm52bXJjID4gL2Rldi9udWxsIHx8IHsgCiAgICAgICAgaWYgWyAiJHtOVk1f
U0lMRU5UOi0wfSIgLW5lIDEgXTsgdGhlbgogICAgICAgICAgICBudm1fZXJyICJXYXJuaW5nOiBV
bmFibGUgdG8gd3JpdGUgdmVyc2lvbiBudW1iZXIgKCRWRVJTSU9OX1NUUklORykgdG8gLm52bXJj
IjsKICAgICAgICBmaTsKICAgICAgICByZXR1cm4gMwogICAgfTsKICAgIGlmIFsgIiR7TlZNX1NJ
TEVOVDotMH0iIC1uZSAxIF07IHRoZW4KICAgICAgICBudm1fZWNobyAiV3JvdGUgdmVyc2lvbiBu
dW1iZXIgKCRWRVJTSU9OX1NUUklORykgdG8gLm52bXJjIjsKICAgIGZpCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'c3RhcnNoaXBfcHJlY21kICgpIAp7IAogICAgU1RBUlNISVBfQ01EX1NUQVRVUz0kPyBTVEFSU0hJ
UF9QSVBFX1NUQVRVUz0oIiR7UElQRVNUQVRVU1tAXX0iKTsKICAgIGlmIFtbIC1uICR7QkxFX0FU
VEFDSEVELX0gJiYgJHsjQkxFX1BJUEVTVEFUVVNbQF19IC1ndCAwIF1dOyB0aGVuCiAgICAgICAg
U1RBUlNISVBfUElQRV9TVEFUVVM9KCIke0JMRV9QSVBFU1RBVFVTW0BdfSIpOwogICAgZmk7CiAg
ICBpZiBbWyAtbiAiJHtCUF9QSVBFU1RBVFVTLX0iIF1dICYmIFtbICIkeyNCUF9QSVBFU1RBVFVT
W0BdfSIgLWd0IDAgXV07IHRoZW4KICAgICAgICBTVEFSU0hJUF9QSVBFX1NUQVRVUz0oIiR7QlBf
UElQRVNUQVRVU1tAXX0iKTsKICAgIGZpOwogICAgam9icyAmPiAvZGV2L251bGw7CiAgICBsb2Nh
bCBqb2IgTlVNX0pPQlM9MCBJRlM9JyAJCic7CiAgICBmb3Igam9iIGluICQoam9icyAtcCk7CiAg
ICBkbwogICAgICAgIFtbIC1uICRqb2IgXV0gJiYgKChOVU1fSk9CUysrKSk7CiAgICBkb25lOwog
ICAgIiR7c3RhcnNoaXBfcHJlY21kX3VzZXJfZnVuYy06fSI7CiAgICBfc3RhcnNoaXBfc2V0X3Jl
dHVybiAiJFNUQVJTSElQX0NNRF9TVEFUVVMiOwogICAgaWYgW1sgLW4gIiR7U1RBUlNISVBfUFJP
TVBUX0NPTU1BTkQtfSIgXV07IHRoZW4KICAgICAgICBldmFsICIkU1RBUlNISVBfUFJPTVBUX0NP
TU1BTkQiOwogICAgZmk7CiAgICBsb2NhbCAtYSBBUkdTPSgtLXRlcm1pbmFsLXdpZHRoPSIke0NP
TFVNTlN9IiAtLXN0YXR1cz0iJHtTVEFSU0hJUF9DTURfU1RBVFVTfSIgLS1waXBlc3RhdHVzPSIk
e1NUQVJTSElQX1BJUEVfU1RBVFVTWypdfSIgLS1qb2JzPSIke05VTV9KT0JTfSIgLS1zaGx2bD0i
JHtTSExWTH0iKTsKICAgIGlmIFtbIC1uICIke1NUQVJTSElQX1NUQVJUX1RJTUUtfSIgXV07IHRo
ZW4KICAgICAgICBTVEFSU0hJUF9FTkRfVElNRT0kKC91c3IvbG9jYWwvYmluL3N0YXJzaGlwIHRp
bWUpOwogICAgICAgIFNUQVJTSElQX0RVUkFUSU9OPSQoKFNUQVJTSElQX0VORF9USU1FIC0gU1RB
UlNISVBfU1RBUlRfVElNRSkpOwogICAgICAgIEFSR1MrPSgtLWNtZC1kdXJhdGlvbj0iJHtTVEFS
U0hJUF9EVVJBVElPTn0iKTsKICAgICAgICBTVEFSU0hJUF9TVEFSVF9USU1FPSIiOwogICAgZmk7
CiAgICBQUzE9IiQoL3Vzci9sb2NhbC9iaW4vc3RhcnNoaXAgcHJvbXB0ICIke0FSR1NbQF19Iiki
OwogICAgaWYgW1sgLW4gJHtCTEVfQVRUQUNIRUQtfSBdXTsgdGhlbgogICAgICAgIGxvY2FsIG5s
bnM9JHtQUzEvL1shJwonXX07CiAgICAgICAgYmxlb3B0IHByb21wdF9ycHMxPSIkbmxucyQoL3Vz
ci9sb2NhbC9iaW4vc3RhcnNoaXAgcHJvbXB0IC0tcmlnaHQgIiR7QVJHU1tAXX0iKSI7CiAgICBm
aTsKICAgIFNUQVJTSElQX1BSRUVYRUNfUkVBRFk9dHJ1ZQp9Cg==' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'c3RhcnNoaXBfcHJlZXhlYyAoKSAKeyAKICAgIGxvY2FsIFBSRVZfTEFTVF9BUkc9JDE7CiAgICBp
ZiBbICIke1NUQVJTSElQX1BSRUVYRUNfUkVBRFk6LX0iID0gInRydWUiIF07IHRoZW4KICAgICAg
ICBTVEFSU0hJUF9QUkVFWEVDX1JFQURZPWZhbHNlOwogICAgICAgIFNUQVJTSElQX1NUQVJUX1RJ
TUU9JCgvdXNyL2xvY2FsL2Jpbi9zdGFyc2hpcCB0aW1lKTsKICAgIGZpOwogICAgOiAiJFBSRVZf
TEFTVF9BUkciCn0K' | base64 -d)" > /dev/null 2>&1
eval "$(echo 'c3RhcnNoaXBfcHJlZXhlY19wczAgKCkgCnsgCiAgICAvdXNyL2xvY2FsL2Jpbi9zdGFyc2hpcCB0
aW1lCn0K' | base64 -d)" > /dev/null 2>&1
# Shell Options
shopt -u autocd
shopt -u assoc_expand_once
shopt -u cdable_vars
shopt -u cdspell
shopt -u checkhash
shopt -u checkjobs
shopt -s checkwinsize
shopt -s cmdhist
shopt -u compat31
shopt -u compat32
shopt -u compat40
shopt -u compat41
shopt -u compat42
shopt -u compat43
shopt -u compat44
shopt -s complete_fullquote
shopt -u direxpand
shopt -u dirspell
shopt -u dotglob
shopt -u execfail
shopt -u expand_aliases
shopt -u extdebug
shopt -u extglob
shopt -s extquote
shopt -u failglob
shopt -s force_fignore
shopt -s globasciiranges
shopt -s globskipdots
shopt -u globstar
shopt -u gnu_errfmt
shopt -u histappend
shopt -u histreedit
shopt -u histverify
shopt -s hostcomplete
shopt -u huponexit
shopt -u inherit_errexit
shopt -s interactive_comments
shopt -u lastpipe
shopt -u lithist
shopt -u localvar_inherit
shopt -u localvar_unset
shopt -s login_shell
shopt -u mailwarn
shopt -u no_empty_cmd_completion
shopt -u nocaseglob
shopt -u nocasematch
shopt -u noexpand_translation
shopt -u nullglob
shopt -s patsub_replacement
shopt -s progcomp
shopt -u progcomp_alias
shopt -s promptvars
shopt -u restricted_shell
shopt -u shift_verbose
shopt -s sourcepath
shopt -u syslog_history
shopt -u varredir_close
shopt -u xpg_echo
set -o braceexpand
set -o hashall
set -o interactive-comments
set -o monitor
set -o onecmd
shopt -s expand_aliases
# Aliases
alias -- ..='cd ..'
alias -- ...='cd ../..'
alias -- ....='cd ../../..'
alias -- build='npm run build'
alias -- c='clear'
alias -- cls='clear'
alias -- code='code .'
alias -- cp='cp -i'
alias -- cs='claude --dangerously-skip-permissions'
alias -- d='docker'
alias -- dc='docker-compose'
alias -- dev='npm run dev'
alias -- df='df -h'
alias -- di='docker images'
alias -- dl='cd ~/Downloads'
alias -- docs='cd ~/Documents'
alias -- dps='docker ps'
alias -- dt='cd ~/Desktop'
alias -- du='du -h'
alias -- egrep='egrep --color=auto'
alias -- fgrep='fgrep --color=auto'
alias -- free='free -h'
alias -- g='git'
alias -- ga='git add'
alias -- gb='git branch'
alias -- gc='git commit'
alias -- gco='git checkout'
alias -- gd='git diff'
alias -- gl='git log --oneline --graph --decorate'
alias -- gp='git push'
alias -- grep='grep --color=auto'
alias -- gs='git status'
alias -- h='cd ~'
alias -- install='sudo dnf install'
alias -- l='ls -CF'
alias -- la='ls -A'
alias -- ll='ls -alF'
alias -- mc='. /usr/libexec/mc/mc-wrapper.sh'
alias -- mv='mv -i'
alias -- pip='pip3'
alias -- ports='sudo netstat -tulpn'
alias -- psg='ps aux | grep'
alias -- py='python3'
alias -- rm='rm -i'
alias -- search='sudo dnf search'
alias -- test='npm test'
alias -- update='sudo dnf update'
alias -- weather='curl wttr.in'
alias -- which='(alias; declare -f) | /usr/bin/which --tty-only --read-alias --read-functions --show-tilde --show-dot'
alias -- xzegrep='xzegrep --color=auto'
alias -- xzfgrep='xzfgrep --color=auto'
alias -- xzgrep='xzgrep --color=auto'
alias -- zegrep='zegrep --color=auto'
alias -- zfgrep='zfgrep --color=auto'
alias -- zgrep='zgrep --color=auto'
# Check for rg availability
if ! command -v rg >/dev/null 2>&1; then
  alias rg='/usr/bin/rg'
fi
export PATH='/home/<USER>/.bun/bin:/home/<USER>/.bun/bin:/home/<USER>/.bun/bin:/home/<USER>/.bun/bin:/home/<USER>/.cargo/bin:/home/<USER>/.nvm/versions/node/v24.3.0/bin:/home/<USER>/.local/bin:/home/<USER>/bin:/sbin:/bin:/usr/bin:/usr/local/bin:/home/<USER>/.dotnet/tools'
