{"permissions": {"allow": ["<PERSON><PERSON>(dnf:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(hostnamectl set-hostname:*)", "<PERSON><PERSON>(hostnamectl)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(sudo dnf remove:*)", "Bash(echo $SHELL)", "Bash(rm:*)", "<PERSON><PERSON>(echo:*)", "Bash(echo $DESKTOP_SESSION)", "Bash(gnome-extensions list:*)", "Bash(gsettings get:*)", "<PERSON><PERSON>(wget:*)", "Ba<PERSON>(unzip:*)", "Bash(gnome-extensions enable:*)", "<PERSON><PERSON>(killall:*)", "Bash(./install.sh:*)", "Bash(gsettings set:*)", "<PERSON><PERSON>(git clone:*)", "Bash(flatpak list:*)", "Bash(rpm:*)", "Bash(ping:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(source:*)", "Bash(~/.bun/bin/bun install:*)", "Bash(ls:*)", "Bash(~/.bun/bin/bun run:*)", "Bash(export PATH=\"$HOME/.bun/bin:$HOME/.cargo/bin:$PATH\")", "Bash(bun run:*)", "Bash(ls:*)", "Bash(flatpak uninstall:*)", "Bash(dotnet --version)", "Bash(sudo dnf install:*)", "<PERSON><PERSON>(busctl:*)", "Bash(/tmp/setup_samba_user.sh:*)", "Bash(/tmp/samba_passwd.exp:*)", "Bash(cp:*)", "Bash(git config:*)", "Bash(starship preset:*)", "Bash(mount:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp get:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__supabase__list_tables", "mcp__desktop-commander__execute_command", "mcp__desktop-commander__read_file", "<PERSON><PERSON>(journalctl:*)", "<PERSON>sh(coredumpctl list:*)", "<PERSON><PERSON>(nvidia-smi:*)", "Bash(systemctl:*)", "Bash(sensors:*)", "Bash(ip:*)", "Bash(systemd-detect-virt:*)", "Bash(grep:*)", "mcp__desktop-commander__write_file", "Bash(find:*)", "Bash(sysctl:*)", "<PERSON><PERSON>(ulimit:*)", "Bash(dmesg:*)", "<PERSON>sh(sudo dmesg:*)", "Bash(modinfo:*)"], "deny": []}}